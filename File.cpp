/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

/* *********************************************************************************//**
 * @defgroup CMDT_IOC
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_IOC
 * @{
 * *************************************************************************************/


#include <iomanip> // setiosflags
#include <iostream>
#include <sstream> // stringstream
#include <string> // stringstream
#include <fstream> // stringstream

using namespace std;

#include "Cli.h"
#include "File.h"


/* *********************************************************************************//**
 * @brief	Close file.
 * @details	void Cli::CloseFile()
 * @details	Close the previously opened file of class member IoFile.
 * *************************************************************************************/

void File::CloseFile()
{
	CloseFile(IoFile) ;
	IoFile = NULL ;
}

void File::CloseFile(fstream *iof)
{
	if (iof != NULL) {
		iof->close() ;
		delete iof ;
	}
}

/* *********************************************************************************//**
 * @brief	Open file.
 * @details	int File::OpenFile(char *file_name, bool write_option)
 * @details	Close any previously opened file and create a new file I/O stream
 *		with ro/rw access. The I/O stream is stored in the class member: 'IoFile'
 * @param [in]	file_name : the file to open preceded by the path name
 * @param [in]	write_option : rw (uenqual zero) else ro
 * @return	IOCTL_CMD_STATUS_OK (file opened) else IOCTL_CMD_STATUS_ERROR
 * *************************************************************************************/

int File::OpenFile(char *file_name, bool write_option)
{
	int	rc ;

	CloseFile() ;
	IoFileSize =  0;

	rc = IOCTL_CMD_STATUS_ERROR ;

	if (write_option)
		IoFile  = new fstream(file_name, ios::out) ;
	else
		IoFile = new fstream(file_name, ios::in) ;

	if (IoFile) {
		if (IoFile->good()) {
			stat( file_name, &IoFileStatus );
			IoFileSize = IoFileStatus.st_size ;
			rc = IOCTL_CMD_STATUS_OK ;
        	}
	}

	return (rc) ;
}

int File::OpenFile(char *file_name, bool write_option, fstream **iof)
{
	int rc = IOCTL_CMD_STATUS_ERROR; // change to default error

	if (write_option)
		*iof  = new fstream(file_name, ios::out) ;
	else
		*iof = new fstream(file_name, ios::in) ;

	if (*iof) {
		if (! (*iof)->good()) {
			CloseFile(*iof) ;
        	}
		else {
			rc = IOCTL_CMD_STATUS_OK;
		}
	}
	return (rc) ;
}

/* *********************************************************************************//**
 * @brief	Read an ASCII file
 * @details	int File::ReadFileAsc(char *buf, streamsize n)
 * @details	Reads a single line from an ASCII file into argument: buf
 * @param [in]	buf : the target buffer where to write the data
 * @param [in]	n : the maximal buffer size to read.
 * @return	number of bytes read to buffer
 * *************************************************************************************/

int File::ReadFileAsc(char *buf, streamsize n, fstream *iof)
{
	int	rc = 0;
	
	if (iof) {
		iof->getline(buf, n, '\n') ;
		rc = iof->gcount() ;
#if 0
		stringstream ss ;
		if (n) {
			int i = 0 ;
			ss << buf ;
			while (n--) {
				int v = 0xffff ;
				ss>>hex>>v;
				if (v == 0xffff) {
					buf[i] = 0 ;
					break ;
				}
				buf[i++] = v & 0xff;
				++rc ;
			}
		}
#endif
	}
	return (rc) ;
}

int File::ReadFileAsc(char *buf, streamsize n)
{
	return (ReadFileAsc(buf, n, IoFile)) ;
}

/* *********************************************************************************//**
 * @brief	Read a binary file
 * @details	int File::ReadFileBin(char *buf, streamsize n)
 * @details	Reads a single line from a binary file into argument: buf
 * @param [in]	buf : the target buffer where to write the data
 * @param [in]	n : the maximal buffer size to read.
 * @return	number of bytes read to buffer
 * *************************************************************************************/

int File::ReadFileBin(char *buf, streamsize n)
{
	return (ReadFileBin(IoFile, buf, n)) ;
}

/* *********************************************************************************//**
 * @brief	Write to binary file 
 * @details	int File::WriteFileBin(char *buf, streamsize n)
 * @details	Write a single line to a binary file from argument: buf
 * @param [in]	buf : the source buffer where to read the data from
 * @param [in]	n : the buffer length to write.
 * @return	number of bytes written 
 * *************************************************************************************/
int File::WriteFileBin(char *buf, streamsize n)
{
	return (WriteFileBin(IoFile, buf, n)) ;
}

int File::ReadFileBin(fstream *iof, char *buf, streamsize n)
{
	if (iof != NULL) {
		return (iof->readsome(buf, n)) ;
	}
	return (0) ;
}

int File::WriteFileBin(fstream *iof, char *buf, streamsize n)
{
	if (iof != NULL) {
		iof->write(buf, n) ;
	}
	else {
		n = 0 ;
	}
	return (n) ;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
