/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef CLI_H
#define CLI_H

#define	CLI_VERSION	"1.00 04/18/18"

#ifdef	LINUX
#include <asm/byteorder.h>
#include <stdint.h>
#include <stdlib.h>
#endif

#include <iostream>
#include <iomanip>
#include <sstream>
#include <fstream>

extern "C" {
#include <string.h>

#ifdef	LINUX
#include <unistd.h> 
#define	in_addr_t	uint32_t
#endif
}               

typedef uint16_t u16;
typedef uint32_t u32;
typedef uint64_t u64;

#define BIT(nr) (1UL << (nr))

using namespace std;

const int IOCTL_CMD_STATUS_OK = 0;
const int IOCTL_CMD_STATUS_ERROR = 1;
const int IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND = 2;
const int IOCTL_CMD_STATUS_ERROR_INVALID_NUM_CMDLINE_PARAMS = 3;
const int IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER = 4;
const int IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND = 5;
const int IOCTL_CMD_STATUS_ERROR_DUPLICATE_ARGUMENT = 6;
const int IOCTL_CMD_STATUS_ERROR_INVALID_DEVICE = 7;
const int IOCTL_CMD_STATUS_ERROR_TIMEOUT = 8;

#define	IOCTL_STATUS_OK (unsigned long)	0

typedef struct CliArgs_s {
	const char	*argument_name ;	// Identifier of argument is a string
	int		min_arguments ;		// Defines the minimal number of additional arguments or -1 if undefined
	int		max_arguments ;		// Defines the maximal number of additional arguments if min_arguments != -1
} CliArgs_t ;

class Cli {
	private:
		int		arg_cnt ;
		char		**arg_val ;
		CliArgs_t	*arg_list ;	// List of supported command specific arguments
		uint32_t	arg_list_size ;

		int 		ArgumentSupported(const char *ref, int *argument_count) ;
	protected:

		bool		l_endian ;	
		int		dump_format ;

		unsigned long   cmd;
		void            *data ;
		char		*appname ;
		char		*cmdhelp ;

		int             err; // internal error number
		bool		verbose ;

					Cli(int argc, char *argv[]) ;

		void  			DisplayPacketAsCharDump(char *, unsigned int, int) ;
		void			DisplayPacketAsHexDump(
							char *,			// buffer to dump
							unsigned int,		// data length
							unsigned int fmt=1,	// display format replaced by dump_format
							bool native = true,	// native memory format
							unsigned int width=16,	// bytes per line
							unsigned int off=0,	// Start at address offset
							unsigned int offi=0);	// Address offset increment, default byte

		void  			DisplayIPAddress(int) ;
		void  			DisplayMacAddress(char *mac, const char *text) ;

		void			SetEndiannes(void) ;
                int			ScanArgList(int argc, char *argv[], const char *ref, uint32_t *pos = NULL, uint32_t *nargs = NULL, char tag = '-');

		int			SetDisplayFormat(char *, int def=1);
		int			cmdHelp(char *);

		uint32_t 		StringToHexInt(char*) ;
		uint32_t 		StringToDecHexInt(char*) ;
		int	 		StringToIPAddress(char*) ;
		uint64_t		StringToMACAddress(char*, unsigned char *dest=NULL) ;
		unsigned long		StringToDecHexLong(char*) ;
		unsigned long long int	StringToDecHexLongLong(char*) ;

		int			CheckArgumentList(int argc, char *argv[], char tag = '-') ;
		virtual CliArgs_t *	SetArgumentList(uint32_t *arg_list_size)	{ *arg_list_size = 0 ; return (NULL) ; }
		virtual int		EvaluateArgumentList(int argc, char *argv[])	{ return (IOCTL_CMD_STATUS_OK) ; }
		static void		ShowArgList(int argc, char *argv[]) ;

	public:
		int 			HandleArguments() ;
                virtual int		Action(void)		{ return err ; };
                virtual int		preAction(void)		{ return err ; };
                virtual int		postAction(void)	{ return err ; };
		virtual string		getErrorMsg() ;
};

#endif // CLI_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
