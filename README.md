Steps to compile the oak driver for cmdtool
-------------------------------------------
1. Copy cmdtool to the driver directory.
2. Copy oak_ctl.c and oak_ctl.h from cmdtool to the Oak driver.
3. Update driver Makefile to enable CMDTOOL macro
    EXTRA_CFLAGS += -DKERNEL -Werror -DCMDTOOL
4. Update driver Makefile to compile oak_ctl.c
    oak_pci-objs := ...oak_chksum.o oak_dpm.o oak_ctl.o
5. Compile cmdtool using 'make' command.
6. Compile driver and load.

使用cmdtool
---------------------------------------------
使用 cli help 查看命令
使用 cli help <command> 查看具体命令的帮助
- cli help phy2112
- cli help phy1512
- cli help forward