/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

/* *********************************************************************************//**
 * @defgroup CMDT_IOC	CMDT IO command processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_IOC
 * @{
 * *************************************************************************************/

#include <iomanip> // setiosflags
#include <iostream>
#include <sstream> // stringstream
#include <string> // stringstream
#include <fstream> // stringstream
#include <arpa/inet.h>

#include "Cli.h"

/* *********************************************************************************//**
 * @brief	Class contructor
 * @details	Cli :: Cli()
 * *************************************************************************************/

Cli :: Cli(int argc, char *argv[])
{
	cmd = 0 ;
	data = NULL ;
	cmdhelp = NULL ;
	dump_format = 0 ;
	SetEndiannes() ;
	// verbose = ScanArgList(argc, argv, "-v") ;
	appname = argv[0] ;

	arg_list = NULL ;
	arg_list_size = 0 ;

	arg_cnt = argc ;
	arg_val = argv ;
}

int Cli :: HandleArguments()
{
 	arg_list = SetArgumentList(&arg_list_size) ;

	err = CheckArgumentList(arg_cnt, arg_val) ;			// Check if all arguemnts are supported
	if (err == IOCTL_CMD_STATUS_OK) {
		err = EvaluateArgumentList(arg_cnt, arg_val) ;	// Extract the argument values from the argument list
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief	Set endiannes for hex dump printouts
 * @details	void Cli :: SetEndiannes()
 * @details	Determine the system endianess and store the value.
 * *************************************************************************************/

void Cli :: SetEndiannes(void)
{
	uint32_t	u32, le ;
	const char	p[4] = { 0x11, 0x22, 0x33, 0x44 } ;

	u32 = (* (uint32_t *) &p[0]) ;
	le =  (* (uint32_t *) &p[0]) ;
	// le  = htole32(le) ;

	if (u32 == le)
		l_endian = true ;
	else 
		l_endian = false ;
#if 0
	if (l_endian)
		cout << "System endianess: LE" << endl ;
	else
		cout << "System endianess: BE" << endl ;
#endif
} 

/* *********************************************************************************//**
 * @brief	Create error message 
 * @details	string Cli :: getErrorMsg()(int m)
 * @return	Checks the class member variable 'err' and returns the corresponding
 *		error string.
 * *************************************************************************************/

string Cli::getErrorMsg()
{
	if (err == IOCTL_CMD_STATUS_OK) {
		return (verbose == true ? "ok" : "") ;
	}
	else if (err == IOCTL_CMD_STATUS_ERROR) 
		return "command failed";
	else if (err == IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND) 
		return "invalid cmd";
	else if (err == IOCTL_CMD_STATUS_ERROR_INVALID_NUM_CMDLINE_PARAMS) 
		return "invalid number of command line parameters";
	else if (err == IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) 
		return "invalid command line";
	else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) 
		return "missing or invalid argument";
	else if (err == IOCTL_CMD_STATUS_ERROR_DUPLICATE_ARGUMENT) 
		return "argument referenced more than once";
	else if (err == IOCTL_CMD_STATUS_ERROR_INVALID_DEVICE) 
		return "invalid device";
	else if (err == IOCTL_CMD_STATUS_ERROR_TIMEOUT) 
		return "command timeout";
	return "unknown error"; 
}

#define	HELP_DIR "./help"

/* *********************************************************************************//**
 * @brief	Display help text
 * @details	int Cli :: cmdHelp(const char *f)
 * @details	Open the help file passed as argument.\n
 *		The default search path is: /usr/src/tools/tools/cmdtool (HELP_DIR).\n
 *		Prints all lines between the tags: \"CMD::<cmdhelp>\" and \"END\"
 *		where member variable 'cmdhelp' contains the command string to seach for.\n
 *		The default command string is \"help\".
 * @param [in]  f : help file string
 * @return	IOCTL_CMD_STATUS_OK or IOCTL_CMD_STATUS_ERROR
 * *************************************************************************************/
int Cli::cmdHelp(char *f)
{
	char line[128];
	int  found ;

	snprintf(line,sizeof(line),"%s/%s",HELP_DIR,f) ;

	fstream inFile(line, ios::in);
	if (! inFile) {
		return(IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND);
	}

	found = 0 ;
	while (inFile.getline(line, sizeof(line))) {
		string s = line ;
		if (found == 0) {
			// size_t	pos ;
			if (s.find("CMD::") != string::npos &&
				s.find(cmdhelp) != string::npos) {
				cout << "Command:" << cmdhelp << endl;
				found = 1 ;
			}
			continue ;
		}
		if (found == 1 && s.find("END") != string::npos)
			break ;
		if (found)
			cout << line << endl;
		
	}

	inFile.close() ;

	return (found ? IOCTL_CMD_STATUS_OK:IOCTL_CMD_STATUS_ERROR) ;
}

/* *********************************************************************************//**
 * @brief	Convert string to signed integer value
 * @details	unsigned int Cli :: StringToDecHexInt(char* num)
 * @param [in]  num : string with standard hexadecimal or decimal notation
 * @return	converted value or 0 (default)
 * @warning	Unrecognized expressions cause a default return of 0.
 *		The function does not return an error code.
 * *************************************************************************************/

uint32_t Cli::StringToDecHexInt(char* num)
{
	std::stringstream number;
	uint32_t value = 0; // initial default

	if (strchr(num, 'x')) { 
		number << hex << num; // some hex number
	} else {
		number << dec << num; // some dec number
	}
	number >> value;
	return value;
}

uint32_t Cli::StringToHexInt(char* num)
{
	std::stringstream number;
	uint32_t value = 0; // initial default

	number << hex << num; // some hex number
	number >> value;
	return value;
}

int Cli::StringToIPAddress(char* num)
{
	int value ;
	inet_pton(AF_INET, num, &value);
	return (value) ;
}

uint64_t Cli::StringToMACAddress(char* addr, unsigned char *dest)
{
	std::string s = addr ;
    	unsigned char a[6];
    	int rc, last = -1;


	rc = sscanf(s.c_str(), "%hhx:%hhx:%hhx:%hhx:%hhx:%hhx%n",
		a + 5, a + 4, a + 3, a + 2, a + 1, a + 0, &last);

    	if(rc != 6 || s.size() != last) {
        	return 0 ;
	}

	if (dest != NULL) {
		dest[0] = a[0] ;
		dest[1] = a[1] ;
		dest[2] = a[2] ;
		dest[3] = a[3] ;
		dest[4] = a[4] ;
		dest[5] = a[5] ;
	}

    	return ( uint64_t(a[0]) << 40 | uint64_t(a[1]) << 32 |
		 uint64_t(a[2]) << 24 | uint64_t(a[3]) << 16 |
		 uint64_t(a[4]) <<  8 | uint64_t(a[5]));
}

/* *********************************************************************************//**
 * @brief	Convert string to unsigned long long integer value
 * @details	unsigned long long Cli :: StringToDecHexLongLong(char* num)
 * @param [in]  num : string with standard hexadecimal or decimal notation
 * @return	converted value or 0 (default)
 * @warning	Unrecognized expressions cause a default return of 0.
 *		The function does not return an error code.
 * *************************************************************************************/

unsigned long long int Cli::StringToDecHexLongLong(char* num)
{
	std::stringstream number;
	unsigned long long int value = 0; // initial default

	if (strchr(num, 'x')) { 
		number << hex << num; // some hex number
	} else {
		number << dec << num; // some dec number
	}
	number >> value;
	return value;
}

/* *********************************************************************************//**
 * @brief	Convert string to unsigned long integer value
 * @details	unsigned long Cli :: StringToDecHexLong(char* num)
 * @param [in]  num : string with standard hexadecimal or decimal notation
 * @return	converted value or 0 (default)
 * @warning	Unrecognized expressions cause a default return of 0.
 *		The function does not return an error code.
 * *************************************************************************************/

unsigned long Cli::StringToDecHexLong(char* num)
{
	std::stringstream number;
	unsigned long value = 0; // initial default

	if (strchr(num, 'x')) { 
		number << hex << num; // some hex number
	} else {
		number << dec << num; // some dec number
	}
	number >> value;
	return value;
}

/* *********************************************************************************//**
 * @brief	Display character dump
 * @details	void Cli :: DisplayPacketAsCharDump(char *p, unsigned int pos, int width)
 * @details	Displays printable characters or a dot if character is not printable.
 * @param [in]  p : source buffer pointer
 * @param [in]  pos : offset into buffer
 * @param [in]  width : number of bytes to display, beginning at offset 'pos'.
 * *************************************************************************************/

void  Cli::DisplayPacketAsCharDump(char *p, unsigned int pos, int width)
{
	unsigned int last = pos + width ;

	// cout << setw(2) << setfill(' ');
	cout << setw(1) << setfill(' ');
	while (pos < last) {
		char c = p[pos++]&0xff;
		if (!isprint(c)) c = '.' ;
		cout << c ;
	}
}

// Set the display format of the DisplayPacketAsHexDump(...)
//
/* *********************************************************************************//**
 * @brief	Specify width of digit display
 * @details	int Cli :: SetDisplayFormat(char *fmt, int def)
 * @param [in]  fmt : format code e.g: Q(uadword),D(ouble),W(ord),B(byte)
 * @param [in]  def : default width to use on code mismatch
 * @return	matched width or default argument
 * *************************************************************************************/

int  Cli::SetDisplayFormat(char *fmt, int def)
{
	switch (toupper(*fmt)) {
	case 'Q': dump_format = 8 ; break ;		// qword
	case 'D': dump_format = 4 ; break ;		// dword
	case 'W': dump_format = 2 ; break ;		// word
	case 'B': dump_format = 1 ; break ;		// byte
	default : dump_format = def ; break ;		// byte
	}
	return (dump_format) ;
}

/* Display a bufer space 'p' as a hex dump. The number of bytes is specified as 'len'.
 * The format fmt specified the width of the particular element e.b. byte(1), word(2), dword(4) ....
 * The argument 'width' specifies the number of bytes per line.
 */
#ifdef	LINUX
#define TO_LE_ENDIAN32(p,i) l_endian ?  0:0
#define TO_LE_ENDIAN16(p,i) l_endian ?  0:0
#else
#define TO_LE_ENDIAN32(p,i) l_endian ?  le32dec(&p[i]) : be32dec(&p[i])
#define TO_LE_ENDIAN16(p,i) l_endian ?  le16dec(&p[i]) : be16dec(&p[i])
#endif

/* *********************************************************************************//**
 * @brief	Display hex dump
 * @details	void Cli :: DisplayPacketAsHexDump()
 * @details	Generate a standardized hex and character dump. The default data 'width'
 *		is 1 byte. This can be overwritten by setting 'dump_format' to a value
 *		unequal zero.
 * @param [in]  p : source data buffer 
 * @param [in]  len : number of bytes to display from source buffer
 * @param [in]  fmt : data width: 1, 2, 4, 8 bytes (default: 1)
 * @param [in]  native : false (enforce LE format), true (default: native format)
 * @param [in]  width : number of data items per line (default: 16)
 * @param [in]  aoff : display address offset (start value, default: 0)
 * @param [in]  offi : increment of address offset (default equals to 'width')
 * @warning	If 'native' is set to false no such character dump is displayed.
 * *************************************************************************************/

void  Cli::DisplayPacketAsHexDump(
	char *p,		// data buffer
	unsigned int	len,	// data length
	unsigned int	fmt,	// format replaced by dump_format: 1,2,4,8
	bool		native,	// how to display data word (native=true, or LE)
	unsigned int	width,	// bytes per line
	unsigned int	aoff,	// start at address offset
	unsigned int	offi)	// address offset incrment per line
{
	uint32_t	u32 ;
	uint16_t	u16 ;
	unsigned long	msk ;
	unsigned int	i = 0, j ;
	unsigned int	off_w ;
	unsigned int    ab = 0 ;

	if (len == 0)
		return ;

	if (dump_format)
		fmt = dump_format ;

	if (fmt > width || fmt == 0) return ;
	if (fmt == 1)
		 native = false ;

	off_w = (len > 0xffff) ? 8:4 ;

	if (offi == 0) offi = width ;

	cout << hex << setiosflags(ios::right) << setfill('0') << setw(off_w) ;
	cout << endl << aoff << ": " ;
	msk = (1<<(fmt*8))-1 ;

	for (j=0; i < len; ) {
 		cout << setw(fmt*2)  ;
		switch (fmt) {
		case 4: {
			if (native)
				u32 = (* (uint32_t *) &p[i]) ;
			else
				u32 = TO_LE_ENDIAN32(p,i) ;
 			cout << u32 << ' ' ;
		}
			break ;
		case 2: {
			if (native)
				u16 = (* (uint16_t *) &p[i]) ;
			else
				u16 = TO_LE_ENDIAN16(p,i) ;
 			cout << u16 << ' ' ;
		}
			break ;
		default:
			int temp = p[i] & msk;
 			cout << temp << ' ' ;
			break ;
		}
		i += fmt ;
 		// cout << setw(fmt*2) << temp << ' ' ;
#if 0
		if ((i % (width/2)) == 0 && i < len) {
			cout << " " ;	// Additional blank
			ab = !ab ;
		}
#endif
		if ((i % width) == 0 && i < len) {
			if (native == false)
				DisplayPacketAsCharDump(p,j,width);
			j = i ;
			// Print offset address:

#if 0
			if (offi)
				aoff += offi ;
			else
				aoff = i ;
#else
			aoff += offi ;
#endif
			cout << setfill('0') << setw(off_w) << endl << aoff << ": " << setw(fmt*2) ;
		}
	}
	if (j < len && native == false) {
		// cout << setfill(' ') << setw(fmt*2) ;
		len = len-j ;
		i %= width ;
		if (i) {
			int n ;
			n = width * 2 + width / fmt ;
			ab = n - i * 2 - i / fmt   ;
			// for (i=0; i < len-1; i += width) cout << ' ' ;
			while (ab--) cout << ' ' ;
		}
		DisplayPacketAsCharDump(p,j,len);
	}
	// cout << endl << "(little endian dump)" << endl;
	// cout << endl ;
}

/* *********************************************************************************//**
 * @brief	Display formatted IP address
 * @details	void Cli :: DisplayIPAddress(int ip)
 * @param [in]  ip : 4 byte IP address to display
 * *************************************************************************************/
void  Cli::DisplayIPAddress(int ip)
{
	size_t	i;
	int	v;
	for (i = 1; i <= sizeof(ip); i++) {
		v = (ip >> 8*(sizeof(ip)-i) & 0xFF) ;
		if (v)
			cout << setw(3) << setfill('0') << dec << v ;
		else	
			cout << "---" ;
		if (i < sizeof(ip))
			cout << "." ;
	}
}

void  Cli::DisplayMacAddress(char *mac, const char *text)
{
	uint16_t a0, a1, a2 ;

	a0 = * (uint16_t *) &mac[0] ;
	a1 = * (uint16_t *) &mac[2] ;
	a2 = * (uint16_t *) &mac[4] ;

        cout << text << hex << setw(4) << setfill('0') << ntohs (a0) ;
        cout << hex << setw(4) << setfill('0') << ntohs (a1) ;
        cout << hex << setw(4) << setfill('0') << ntohs (a2) ;
}

/* *********************************************************************************//**
 * @brief	Main command execution 
 * @details	int Cli :: Action(void)
 * @details	Open target device and send IO control to device.\n
 *		IO control must be predefined in the member variables: 'cmd' and 'data'\n
 *		Close device
 * @return	IOCTL_CMD_STATUS_OK (ok),\n
 *		IOCTL_CMD_STATUS_ERROR (general error),\n
 *		IOCTL_CMD_STATUS_ERROR_INVALID_DEVICE (could not open device)
 * *************************************************************************************/
#if 0
int Cli::Action(void)
{
	int		rc = IOCTL_CMD_STATUS_ERROR ;

	if (data) {
		if (OpenDevice()) {
			rc = ioctl(fd, cmd, data);
			if (rc >= 0)
				rc = IOCTL_CMD_STATUS_OK ;
			else
				rc = IOCTL_CMD_STATUS_ERROR ;
			CloseDevice() ;
		}
		else 
			rc = IOCTL_CMD_STATUS_ERROR_INVALID_DEVICE ;
	}
	err = rc ;
        return rc; // no postAction by default
}
#endif

int Cli::ArgumentSupported(const char *ref, int *argument_count)
{
	int rc ;

	if (arg_list != NULL) {
		rc = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND ;	// argument is not supported
		for (uint32_t i=0; i < arg_list_size; i++) {
			// cout << "A" << i << ":" <<  this->arg_list[i].argument_name << " " << ref << endl ;
			if (strcmp(ref,this->arg_list[i].argument_name) == 0) {
				if ((argument_count != NULL) && (this->arg_list[i].min_arguments != -1)) {
					if ((*argument_count >= this->arg_list[i].min_arguments) &&
						*argument_count <= this->arg_list[i].max_arguments) {
						rc = IOCTL_CMD_STATUS_OK ;
					}
					else {
						rc = IOCTL_CMD_STATUS_ERROR_INVALID_NUM_CMDLINE_PARAMS ;
					}
				}
				else {
					rc = IOCTL_CMD_STATUS_OK ;
				}
			}
		}
	}
	else {
		rc = IOCTL_CMD_STATUS_OK ;	// default supported
	}
	return (rc) ;
}

int Cli::CheckArgumentList(int argc, char* argv[], char tag)
{
	int	rc = IOCTL_CMD_STATUS_OK ;

	if (arg_list != NULL && arg_list_size > 0) {
		// Validate argument list for unknown arguments ...
		for (int i = 0 ; (i < argc) && (rc == IOCTL_CMD_STATUS_OK) ; i++) {
			if (*argv[i] == tag) {
				rc = IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
				for (int n = 0 ; n < arg_list_size ; n++) {
					if (strcmp(argv[i],arg_list[n].argument_name) == 0) {
						rc = IOCTL_CMD_STATUS_OK ;
					}
				}
				if (rc == IOCTL_CMD_STATUS_OK) {
						for (int n=i+1 ; n < argc ; n++) {
						if (strcmp(argv[i],argv[n]) == 0) {
							return (IOCTL_CMD_STATUS_ERROR_DUPLICATE_ARGUMENT) ;
						}
					}
				}
			}
		}
	}
	return (rc) ;
}

int Cli::ScanArgList(int argc, char *argv[], const char *ref, uint32_t *pos, uint32_t *nargs, char tag)
{
	if (pos != NULL)   *pos = 0 ;
	if (nargs != NULL) *nargs = 0 ;

	for (int i = 0 ; i < argc; i++) {
		if (strcmp(argv[i], ref) == 0) {
			// return (IOCTL_CMD_STATUS_ERROR_DUPLICATE_ARGUMENT) ;
			if (pos) {
				*pos = i+1 ;	// return value > 0 
				// Count the number of additional parameters per option
				if (nargs != NULL) {
					int n = i ;
					while ((++n < argc) && (*argv[n] != tag)) {
						 ++*nargs ;
					}
				}
			}
			return(ArgumentSupported(ref, (int *) nargs)) ;
		}
	}
	return (IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) ;
}

void Cli::ShowArgList(int argc, char *argv[])
{
	cout << "Command args " << argc << ":" ;
	for (int i = 0 ; i < argc; i++) {
		cout << " " << argv[i] ;
	}
	cout << endl ;
}

/* *********************************************************************************//**
 * @brief	Main command execution 
 * @details	int DriverCli :: Action(void)
 * @details	Open datagram socket and send IO control to device via the socket.\n
 *		IO control must be predefined in the member structure: 'cmd'\n
 *		Close socket
 * @return	IOCTL_CMD_STATUS_OK (ok),\n
 *		IOCTL_CMD_STATUS_ERROR (general error),\n
 *		IOCTL_CMD_STATUS_ERROR_INVALID_DEVICE (could not open socket)
 * *************************************************************************************/
#if 0
int DriverCli::Action(void)
{
	int fd = socket(AF_INET, SOCK_DGRAM, 0);
	if (fd != -1) {
		struct ifreq ifr;
		int result;

		strncpy(ifr.ifr_name,(const char*) netif,sizeof(ifr.ifr_name));
		ifr.ifr_addr.sa_family = AF_INET;
		ifr.ifr_data = (caddr_t) &cmd;
#ifdef XXX
		cmd.status = IOCTL_STATUS_OK ;
#endif
		result = ioctl(fd, req, (caddr_t) &ifr);
		err = IOCTL_CMD_STATUS_ERROR; // change to default error
#if 0
		cout << "ACTION result: " << hex << result << " status: " << cmd.status << " &status: " << &cmd.status << "\n" ;
#endif
#ifdef	XXX
		if (result != -1 && (cmd.status == IOCTL_STATUS_OK)) {
			err = IOCTL_CMD_STATUS_OK;
		} 
#endif
		close(fd); 
	}
	else
		err = IOCTL_CMD_STATUS_ERROR_INVALID_DEVICE ;
	return err;
}
#endif

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
