/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef FILE_H
#define FILE_H

extern "C" {
#include <sys/stat.h> 
#include <fcntl.h>
}

class File {

	protected:
		fstream		*IoFile ;
		size_t		IoFileSize ;
		struct stat	IoFileStatus  ;
	public:
				File(int argc, char *argv[]) { } ;
		int		OpenFile(char *, bool) ;
		int		OpenFile(char *, bool, fstream **) ;
		void		CloseFile() ;
		void		CloseFile(fstream *) ;

		size_t		GetFileSize() { return (IoFileSize) ; }

		int		ReadFileAsc(char *, streamsize);
		int		ReadFileAsc(char *, streamsize, fstream *iof);

		int		ReadFileBin(char *, streamsize);
		int		ReadFileBin(fstream *, char *, streamsize);

		int		WriteFileBin(char *, streamsize);
		int		WriteFileBin(fstream *,char *, streamsize);
};

#endif // FILE_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
