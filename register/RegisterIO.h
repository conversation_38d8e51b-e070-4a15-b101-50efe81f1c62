/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef REGISTER_IO_H
#define REGISTER_IO_H

#include "Socket.h"
#include "oak_ioc_reg.h"

/* The register specific structures below are used to manipulate
 * register sets on a bit level.
 */
typedef struct REG_Field_s {
        uint32_t        b_pos_e ;       // End bit position
        uint32_t        b_pos_s ;       // Start bit position
        const char  *   b_descr ;       // Text description
        uint32_t        data ;          // Bit field data
} REG_Field_t ;

typedef struct REG_Register_s {
        uint32_t        RegOffs;       // Register offset
        uint32_t        FieldCnt ;     // Number of bit fields below
        REG_Field_t  *  REGFlds ;      // Bit fields
        uint32_t        data ;         // Data of all bits in REGFlds
        uint32_t        update ;       // Data update flag
} REG_Register_t ;

typedef struct REG_All_Registers_s {
        uint32_t        Elements ;      // Number of elements in Action below
        REG_Register_t *Action ;
} REG_All_Registers_t ;

#define REG_FIELD_SIZE(f)	(sizeof(f)/sizeof(REG_Field_t))
#define REG_REGISTER_SIZE(f)	(sizeof(f)/sizeof(REG_Register_t))

class RegisterIO : public Cli, public Socket
{
	public:
				RegisterIO(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;
		void		SetIfName(char *ifn) { if_name = ifn ; }
		

	static	bool		isResponsible(int argc, char* argv[]) ;

	protected:
	private:
		char	*	if_name ;
		int		ioc_cmd ;
		uint32_t	ioc_val ;
		uint32_t	if_req_count ;

		/* 0:normal register access, 1:read-modify-write-or, 2:read-modify-write-and */
		int		ioc_flag ;
		oak_ioc_reg	ioc_data ;
} ;

#endif // REGISTER_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
