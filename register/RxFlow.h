/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef RXFLOW_IO_H
#define RXFLOW_IO_H

#include "Socket.h"
#include "oak_ioc_flow.h"

class RxFlow : public Cli, public Socket
{
	public:
				RxFlow(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;
		void		SetIfName(char *ifn) { if_name = ifn ; }
		

	static	bool		isResponsible(int argc, char* argv[]) ;

	protected:
	private:
		char	*	if_name ;
		int		ioc_cmd ;
		uint32_t	ioc_val ;

		/* 0:normal register access, 1:read-modify-write-or, 2:read-modify-write-and */
		oak_ioc_flow	ioc_data ;
} ;

#endif // RXFLOW_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
