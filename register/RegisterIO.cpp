/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-rd",	1, 1 },	// Read register : offset
        { "-dev",	1, 1 },	// Read device number
        { "-dump",	2, 2 },	// Read register : offset
        { "-wr",	2, 2 },	// Write register: offset+value
        { "-w0",	2, 2 },	// Wait for zero : offset+bit
        { "-w1",	2, 2 },	// Wait for one  : offset+bit
        { "-or",	2, 2 },	// Read register and 'or' value  : offset+value
        { "-and",	2, 2 },	// Read register and 'and' value  : offset+value
        { "-v",		0, 0 },	// verbose option
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     RegisterIO::RegisterIO(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

RegisterIO::RegisterIO(int argc, char* argv[]) : Cli(argc, argv)
{
	if_name = NULL ;
}

CliArgs_t * RegisterIO :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int RegisterIO :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	if (strcmp(argv[1], "mac") == 0) {
		ioc_cmd = OAK_IOCTL_REG_MAC_REQ ;
		ioc_data.offs = 0x50000 ;
	}
	else if (strcmp(argv[1], "gicu") == 0) {
		ioc_cmd = OAK_IOCTL_REG_MAC_REQ ;
		ioc_data.offs = 0x70000 ;
	}
	else if (strcmp(argv[1], "pcie") == 0) {
		ioc_cmd = OAK_IOCTL_REG_MAC_REQ ;
		ioc_data.offs = 0x80000 ;
	}
	else if (strcmp(argv[1], "esu") == 0) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.offs = 0x10000 ;
	}
	else if (strcmp(argv[1], "ahsi") == 0 ) {
		ioc_cmd = OAK_IOCTL_REG_AHSI_REQ ;
		ioc_data.offs = 0x50000 ;
        }
	else {	/* dump and other commands */
		ioc_cmd = OAK_IOCTL_REG_MAC_REQ ;
		ioc_data.offs = 0x00000 ;
	}

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	ioc_flag = 0 ;
	if_req_count = 0 ;

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-dev", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		ioc_data.dev_no = StringToDecHexInt(argv[pos]) ;
	}

	err = ScanArgList(argc, argv, "-rd", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		// ioc_cmd = mac == 1 ? OAK_IOCTL_REG_MAC_REQ : OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.cmd = OAK_IOCTL_REG_RD ;
		ioc_data.offs += StringToDecHexInt(argv[pos]) ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-dump", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 2) {
		// ioc_cmd = mac == 1 ? OAK_IOCTL_REG_MAC_REQ : OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.cmd = OAK_IOCTL_REG_RD ;
		ioc_data.offs += StringToDecHexInt(argv[pos]) ;
		if_req_count = StringToDecHexInt(argv[pos+1]) ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-wr", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 2) {
		// ioc_cmd = mac == 1 ? OAK_IOCTL_REG_MAC_REQ : OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.cmd = OAK_IOCTL_REG_WR ;
		ioc_data.offs += StringToDecHexInt(argv[pos]) ;
		ioc_data.data = StringToDecHexInt(argv[pos+1]) ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-w0", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		// ioc_cmd = mac == 1 ? OAK_IOCTL_REG_MAC_REQ : OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.cmd = OAK_IOCTL_REG_WC ;
		ioc_data.offs += StringToDecHexInt(argv[pos]) ;
		ioc_data.data = StringToDecHexInt(argv[pos+1]) ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-w1", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		// ioc_cmd = mac == 1 ? OAK_IOCTL_REG_MAC_REQ : OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.cmd = OAK_IOCTL_REG_WS ;
		ioc_data.offs += StringToDecHexInt(argv[pos]) ;
		ioc_data.data = StringToDecHexInt(argv[pos+1]) ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-or", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		// ioc_cmd = mac == 1 ? OAK_IOCTL_REG_MAC_REQ : OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.cmd = OAK_IOCTL_REG_RD ;
		ioc_data.offs += StringToDecHexInt(argv[pos]) ;
		ioc_val = StringToDecHexInt(argv[pos+1]) ;
		ioc_flag = 1 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-and", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		// ioc_cmd = mac == 1 ? OAK_IOCTL_REG_MAC_REQ : OAK_IOCTL_REG_ESU_REQ ;
		ioc_data.cmd = OAK_IOCTL_REG_RD ;
		ioc_data.offs += StringToDecHexInt(argv[pos]) ;
		ioc_val = StringToDecHexInt(argv[pos+1]) ;
		ioc_flag = 2 ;
		return (err) ;
	}

	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int RegisterIO::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int RegisterIO::postAction(void)
{ 
}

int RegisterIO::Action(void)
{ 
	uint32_t count = if_req_count ;

	ioc_data.error = -1 ;

	err = IOCTL_CMD_STATUS_ERROR ;

	if (SetDevice(if_name) == true) {

		do {
			err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;

			if (err == 0 && ioc_flag != 0 && ioc_data.error == 0) {	/* read-modify-write access */
				if (ioc_flag == 1) {
					ioc_data.data |= ioc_val ;
				}
				else {
					ioc_data.data &= ioc_val ;
				}
				ioc_data.cmd = OAK_IOCTL_REG_WR ;
				err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
			}

			if (verbose) {
				cout << "IOC: cmd:0x" << hex << ioc_cmd << "-0x" << ioc_data.cmd << " offs:0x" << setw(8) << setfill('0') << ioc_data.offs << " data:0x"
		 			<< ioc_data.data << dec << " rc:" << err << endl ;
			}
			else if (ioc_data.cmd == OAK_IOCTL_REG_RD) {
				cout << "0x" << hex << setw(8) << setfill('0') << ioc_data.data << endl  ;
			}
			if (ioc_data.error != 0) {
				cout << "IOC: failed with error: " << dec << ioc_data.error << endl ;
				break ;
			}

			if (count > 0) {
				++ioc_data.offs ;	/* Increment register offset */
				--count ;
			}
		} while (count > 0) ;
		verbose = false ;
	}


	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool RegisterIO::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool RegisterIO::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 3) {
		if (	strcmp(argv[1], "mac") == 0 ||
			strcmp(argv[1], "esu") == 0 ||
			strcmp(argv[1], "gicu")== 0 ||
			strcmp(argv[1], "pcie")== 0 ||
			strcmp(argv[1], "ahsi")== 0 ||
			strcmp(argv[1], "gen")== 0 ) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
