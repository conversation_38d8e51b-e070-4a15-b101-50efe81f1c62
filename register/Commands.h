/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef REGISTER_COMMANDS_H
#define REGISTER_COMMANDS_H

/* define all supported commands by name ...
 *
 * HOW TO define a new command ?
 *
 * a) append a new CMD_TABLE entry below that contains
 *    the class identifier of the command class
 * b) eventually create a new subdirectory relative to
 *    the current one e.g.: ../my-new-module
 *    (existing sub-directories are: driver and dsdd)
 * c) write new <command>.cpp and <command>.h file
 *    (copy from any existing file)
 * d) add an entry for the include file to ../IoctlCmdFactory.h:
 *    e.g.: #include "<my-module>/<my-command>.h
 * e) append file ojbect to ../Makefile
 */

/* If there is no such entry for <my-module>_COMMAND_TABLE in
 * the file ../IoctlCmdFactory.cpp you may add a new entry there.
 */

#define	REGISTER_COMMAND_TABLE	\
	CMD_TABLE(RxFlow) ;	\
	CMD_TABLE(RegisterIO) ;	\
	CMD_TABLE(RegisterFileIO) ;

#endif // REGISTER_COMMANDS_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
