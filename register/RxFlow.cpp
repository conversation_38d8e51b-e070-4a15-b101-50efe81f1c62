/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-c",		1, 1 },		// Destination queue
        { "-mgmt",	0, 1 },	// Managemmet frame
        { "-qpri",	0, 1 },	// QPRI filter
        { "-spid",	0, 1 },	// SPID filter
        { "-flow",	0, 1 },	// Flow ID
        { "-DA",	0, 1 },	// DA match
        { "-ET",	0, 2 },	// Ether type match
        { "-fid",	0, 1 },	// FID match
        { "-v",		0, 0 },	// verbose option
        { "-off",	0, 0 },	// Disable filter
        { "-clear",	0, 0 },	// Clear all ring mappings
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     RxFlow::RxFlow(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

RxFlow::RxFlow(int argc, char* argv[]) : Cli(argc, argv)
{
	if_name = NULL ;
}

CliArgs_t * RxFlow :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int RxFlow :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	ioc_cmd = OAK_IOCTL_RXFLOW ;
	ioc_data.idx = 0 ;
	ioc_data.val_lo = 0 ;
	ioc_data.val_hi = 0 ;
	ioc_data.ena = 1 ;
	ioc_data.error = 0 ;
	ioc_data.cmd = 0 ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if (err != IOCTL_CMD_STATUS_OK) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-off", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.ena = 0 ;
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-c", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		ioc_data.idx = StringToDecHexInt(argv[pos]) ;
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-mgmt", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.cmd = OAK_IOCTL_RXFLOW_MGMT ;
		if (ioc_data.ena == 1) {
			if (num == 1) {
				ioc_data.val_lo = StringToDecHexInt(argv[pos]) ;
			}
			else {
				return (IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) ;
			}
		}
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-qpri", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.cmd = OAK_IOCTL_RXFLOW_QPRI ;
		if (ioc_data.ena == 1) {
			if (num == 1) {
				ioc_data.val_lo = StringToDecHexInt(argv[pos]) ;
			}
			else {
				return (IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) ;
			}
		}
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-spid", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.cmd = OAK_IOCTL_RXFLOW_SPID ;
		if (ioc_data.ena == 1) {
			if (num == 1) {
				ioc_data.val_lo = StringToDecHexInt(argv[pos]) ;
			}
			else {
				return (IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) ;
			}
		}
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-flow", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.cmd = OAK_IOCTL_RXFLOW_FLOW ;
		if (ioc_data.ena == 1) {
			if (num == 1) {
				ioc_data.val_lo = StringToDecHexInt(argv[pos]) ;
			}
			else {
				return (IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) ;
			}
		}
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}
	err = ScanArgList(argc, argv, "-DA", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.cmd = OAK_IOCTL_RXFLOW_DA ;
		if (ioc_data.ena == 1) {
			if (num == 1) {
				uint64_t u64 = StringToMACAddress(argv[pos], (unsigned char *) ioc_data.data) ;
#if 0
				cout << "DA:" << hex << (int) ioc_data.data[0] << (int) ioc_data.data[1] << (int) ioc_data.data[2] <<
					(int) ioc_data.data[3] << (int)ioc_data.data[4] <<(int) ioc_data.data[5] << endl ;
#endif
			}
			else {
				return (IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) ;
			}
		}
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-ET", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.cmd = OAK_IOCTL_RXFLOW_ET ;
		if (ioc_data.ena == 1) {
			if (num == 2) {
				ioc_data.val_lo = StringToDecHexInt(argv[pos]) ;
				ioc_data.val_hi = StringToDecHexInt(argv[pos+1]) ;
			}
			else {
				return (IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) ;
			}
		}
	}
	else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-fid", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_data.cmd = OAK_IOCTL_RXFLOW_FID ;
		if (ioc_data.ena == 1) {
			if (num == 1) {
				ioc_data.val_lo = StringToDecHexInt(argv[pos]) ;
			}
			else {
				return (IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER) ;
			}
		}
	}
	else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		err = IOCTL_CMD_STATUS_OK ;
	}

	err = ScanArgList(argc, argv, "-clear", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (ioc_data.cmd == 0) {
			ioc_data.cmd = OAK_IOCTL_RXFLOW_CLEAR ;
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
		}
	}
	else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
		err = IOCTL_CMD_STATUS_OK ;
	}

	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int RxFlow::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int RxFlow::postAction(void)
{ 
}

int RxFlow::Action(void)
{ 

	err = IOCTL_CMD_STATUS_ERROR ;

	if (if_name != NULL) {
		if (SetDevice(if_name) == true) {

			err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;

			if (verbose == 1) {
			}

			if (ioc_data.error != 0) {
				cout << "IOC: failed with error: " << dec << ioc_data.error << endl ;
			}
		}
	}
	else {
		err = IOCTL_CMD_STATUS_ERROR_INVALID_DEVICE ;
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool RxFlow::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool RxFlow::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 3) {
		if (strcmp(argv[1], "rxmap") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
