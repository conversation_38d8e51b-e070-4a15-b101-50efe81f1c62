/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

static CliArgs_t my_arg_list[] = {
	{ "-f",		1, 1 },	// Just 1 argument == file name

	/* the following arguments are inherited from: RegisterIO.cpp */
	{ "-if",	1, 1 },	// Interface name
        { "-rd",	1, 1 },	// Read register : offset
        { "-dump",	2, 2 },	// Read register : offset
        { "-wr",	2, 2 },	// Write register: offset+value
        { "-w0",	2, 2 },	// Wait for zero : offset+bit
        { "-w1",	2, 2 },	// Wait for one  : offset+bit
        { "-or",	2, 2 },	// Read register and 'or' value  : offset+value
        { "-and",	2, 2 },	// Read register and 'and' value  : offset+value
        { "-v",		0, 0 },	// verbose option
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     RegisterFileIO::RegisterFileIO(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

RegisterFileIO::RegisterFileIO(int argc, char* argv[]) : RegisterIO(argc, argv), File(argc, argv)
{
	file_buf[0] = 0 ;
	file_size = sizeof(file_buf) ; // Use default 
	byte_read = 0 ;
	next_input_file = 0 ;
	if_name = NULL ;
}

CliArgs_t * RegisterFileIO :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int RegisterFileIO :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-f", &pos, &num_of_input_files) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		for (uint32_t i = 0 ; i < num_of_input_files; i++) {
			file_name[i] = argv[pos++] ;
		}
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

        err = ScanArgList(argc, argv, "-if", &pos, &num) ;
        if (err == IOCTL_CMD_STATUS_OK) {
                if_name = argv[pos] ;
        }
        else {
		err = IOCTL_CMD_STATUS_OK ;
        }

	// cout << "RegisterFileIO if = " << if_name << endl ;

	return (err) ;
}

/* *********************************************************************************//**
 * @brief       File open
 * @details     int RegisterFileIO::preAction(void)
 * @details     Opens the file in read-only mode.
 * @return      returns the file descriptor
 * *************************************************************************************/

int RegisterFileIO::preAction(void)
{
	err = OpenFile(file_name[next_input_file],false) ;
	return (err) ;
}

void RegisterFileIO::ContinueWithRead(void)
{
	file_end = false ;
	file_size = sizeof(file_buf) ;
}

bool RegisterFileIO::IsLastInputFile()
{
	if ((next_input_file + 1) < num_of_input_files) {
		return (false) ;
	}
	return (true) ;
}

bool RegisterFileIO::NextInputFile()
{
	if ((next_input_file + 1) < num_of_input_files) {
		++next_input_file ;
		return (true) ;
	}
	return (false) ;
}

/* *********************************************************************************//**
 * @brief       Read data from file
 * @details     int RegisterFileIO::action(void)
 * @details     Reads the contents of the file in a loop and prints a hex dump.
 * @return      IOCTL_CMD_STATUS_OK (ok), IOCTL_CMD_STATUS_ERROR (on IO failure)
 * *************************************************************************************/

#if 0
int RegisterFileIO::Action(void)
{
	int	rc = IOCTL_CMD_STATUS_OK ;
	size_t	sum ;
	size_t	off;
	int	rlen ;
	int	offs ;


	byte_read = 0 ;
	cmd = 0;
	off = 0 ;
	sum = 0 ;
	file_end = false ;
	rlen= 0 ;

	IoFile->seekg(file_offs) ;
	offs = file_offs ;

	file_size = GetFileSize() ;

	while (!file_end && file_size) {
		if (off == 0) {
			if (file_size > sizeof(file_buf))
				rlen = sizeof(file_buf) ;
			else {
				rlen = file_size ;
				file_end = true ;
			}
		}
		// rlen = ReadFileBin(&file_buf[off],rlen) ;
		rlen = ReadFileAsc(&file_buf[off],rlen) ;

		if (rlen <= 0) {
			if (off != 0) {
				// DumpBuffer(file_buf,sum,offs) ;
				ExecuteLine(file_buf,sum,offs) ;
			}
			break ;
		}
		sum += rlen ;

		// Read the rest until file_buf is filled ...
		//
		if (!file_end && (sum < sizeof(file_buf))) {
			off = rlen ;
			rlen= sizeof(file_buf) - rlen ;
		} 
		else {	// Display total file_buf or rest at eof
			// DisplayPacketAsHexDump(file_buf,sum,1,true,16,offs) ;
			rlen -= off ;
			file_size -= rlen ;
			off = 0 ;
			byte_read += sum ;
			// DumpBuffer(file_buf,sum,offs) ;
			ExecuteLine(file_buf,sum,offs) ;
			sum = 0 ;
			offs += rlen ;
		}
	}
	return (rc) ;
}
#else
int RegisterFileIO::Action(void)
{
	int	rlen ;
	int	rc = IOCTL_CMD_STATUS_OK ;

	while (true) {
		rlen = ReadFileAsc(file_buf,sizeof(file_buf)) ;
		byte_read += rlen ;
		if (rlen <= 0)
			break ;
		ExecuteLine(file_buf, rlen, 0) ;
		
	}
	return (rc) ;
}
#endif

int RegisterFileIO::ExecuteLine(char *buf, unsigned int len, unsigned int offs)
{
	int	rc, argc ;
	char	*p, *argv[32] ;

	rc = IOCTL_CMD_STATUS_OK ;

	/* Set a generic device interface if we got an interface name for all command lines
	 * in the file. 
	 */
	if (if_name != NULL) {
		SetIfName(if_name) ;
	}

	argc = 0 ;
	p = &buf[offs] ;

	argv[argc++] = (char *) "" ;
	
	while ((argc < 29) && (len > 0) && (*p !='\0')) {
		// cout << "line " << argc << endl ;
		while (*p == ' ' || *p == '\t') {
			*p++ = '\0' ;
			--len ;
		}
		if (*p == '\n') {
			*p = '\0' ;
			break ;
		}

		argv[argc] = p ;
		++argc ;

		while (*p != '\0' && *p != ' ' && *p != '\t') {
			++p ;
			--len ;
		}
	}
#if 0
	if (argc > 0) {
		argv[argc++] = (char *) "-if" ;
		argv[argc++] = (char *) "p4p1" ;
	}
#endif
#if 0
	for (int i = 0 ; i < argc; i++) {
		cout << "<" << argv[i] << ">" ;
	}
	cout << endl ;
#endif
	if (argc > 0) {
		rc = RegisterIO :: EvaluateArgumentList(argc, argv) ;
		// cout << "ERR:"  << rc << endl ;
		if (rc == IOCTL_CMD_STATUS_OK) {
			rc = RegisterIO::Action() ;
			// verbose = false ;
		}
	}

	return (rc) ;
	// DumpBuffer(buf,len,offs) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int RegisterFileIO::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/

int RegisterFileIO::postAction(void)
{
	if (err == IOCTL_CMD_STATUS_OK) { // was initialization ok?
		cout << endl << dec << byte_read << " bytes read" << endl ;
	}
	CloseFile() ;
	return err;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool RegisterFileIO::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool RegisterFileIO::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;

	if (argc >= 2 && strcmp(argv[1], "exec") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
