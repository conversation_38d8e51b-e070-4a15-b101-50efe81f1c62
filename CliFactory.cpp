/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

/* *********************************************************************************//**
 * @defgroup CMDT_IOC	CMDT IO command processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_IOC
 * @{
 * *************************************************************************************/

#include <iostream>
#include "CliFactory.h"

using namespace std;

/* *********************************************************************************//**
 * @brief	Create an IOCTL command object
 * @details	Cli * CliFactory::CreateCli(int argc, char* argv[])
 * @details	The function scans the lists of command that are provided within
 *		the files: \<subdir\>/ Commands.h. If a command passed in the argument
 *		matches a command class then an object is created and returned.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return	A pointer to an object's base class in case of a command match.	
 * *************************************************************************************/
Cli* CliFactory::CreateCli(int argc, char* argv[])
{
	char	*appname = argv[0] ;
	string	cmd_list ;
	string	cmd_fail ;

	CMDTOOL_CMD_TABLE(cmd_list, cmd_fail) ;

	if (cmd_fail.empty()) {
		cerr << endl << "Version: " << CLI_VERSION << " (Author: afischer)" ;
		cerr << endl << "Syntax error, use: " << appname << " [ help ] <command> ... " << endl ;
		cerr << "commands : " << cmd_list << endl ; 
		cerr << "Common command examples:" << endl;
		cerr << "	" << appname << " help phy2112" << endl;
		cerr << "	" << appname << " help forward" << endl;
		cerr << "	" << appname << " help ESTAT" << endl;
	}
	return NULL ;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
