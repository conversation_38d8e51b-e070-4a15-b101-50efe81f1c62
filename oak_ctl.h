/* 
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *  
 *        All Rights Reserved
 *  
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *  
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *  
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *  
 *        The information in this file is provided "AS IS" without warranty.
 * 
 * Contents: collection of functions that provide user IO-control requests
 * 
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class Model::oak_ctl
 * Author: afischer
 * Date  :2019-12-16  - 11:04
 * 
 *  */
#ifndef H_OAK_CTL
#define H_OAK_CTL

#include "oak_unimac.h" /* Include for relation to classifier oak_unimac */
#include "oak_net.h" /* Include for relation to classifier oak_net */

extern uint32_t debug;
/* Name      : channel_status_access
 * Returns   : int
 * Parameters:  oak_t * np = np,  struct ifreq * ifr = ifr,  int cmd = cmd
 *  */
int oak_ctl_channel_status_access(oak_t* np, struct ifreq* ifr, int cmd);

/* Name      : set_mac_rate
 * Returns   : int
 * Parameters:  oak_t * np = np,  struct ifreq * ifr = ifr,  int cmd
 *  */
int oak_ctl_set_mac_rate(oak_t* np, struct ifreq* ifr, int cmd);

/* Name      : set_rx_flow
 * Returns   : int
 * Parameters:  oak_t * np = np,  struct ifreq * ifr = ifr,  int cmd = cmd
 *  */
int oak_ctl_set_rx_flow(oak_t* np, struct ifreq* ifr, int cmd);

/* Name      : set_txr_rate
 * Returns   : int
 * Parameters:  oak_t * np = np,  struct ifreq * ifr = ifr,  int cmd = cmd
 *  */
int oak_ctl_set_txr_rate(oak_t* np, struct ifreq* ifr, int cmd);

/* Name      : direct_register_access
 * Returns   : int
 * Parameters:  oak_t * np,  struct ifreq * ifr,  int cmd
 *  */
int oak_ctl_direct_register_access(oak_t* np, struct ifreq* ifr, int cmd);

#endif /* #ifndef H_OAK_CTL */

