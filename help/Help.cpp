/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "CliFactory.h"
#include "HelpTexts.h"
const FileHelp::HelpEntry FileHelp::HELP_ENTRIES[] = {
    {"dump", HelpTexts::DUMP_HELP},
    {"exec", HelpTexts::EXEC_HELP},
	{"esu", HelpTexts::ESU_HELP},
    {"load", HelpTexts::LOAD_HELP},
    {"lgen", HelpTexts::LGEN_HELP},
    {"mac", HelpTexts::MAC_HELP},
    {"showc", HelpTexts::SHOWC_HELP},
    {"upload", HelpTexts::UPLOAD_HELP},
    {"atu", HelpTexts::ATU_HELP},
    {"vtu", HelpTexts::VTU_HELP},
    {"tcam", HelpTexts::TCAM_HELP},
    {"estat", HelpTexts::ESTAT_HELP},
	{"ESTAT", HelpTexts::ESTAT_HELP},
    {"qbv", HelpTexts::QBV_HELP},
    {"set", HelpTexts::SET_HELP},
    {"rxmap", HelpTexts::RXMAP_HELP},
    {"irq", HelpTexts::IRQ_HELP},
    {"phy2112", HelpTexts::PHY2112_HELP},
	{"phy1512", HelpTexts::PHY1512_HELP},
	{"forward", HelpTexts::FORWARD_HELP},
	{"PHY2112", HelpTexts::PHY2112_HELP},
	{"PHY1512", HelpTexts::PHY1512_HELP},
	{"FORWARD", HelpTexts::FORWARD_HELP},
};
/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Help information for file IO
 * @details     FileHelp::FileHelp(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

FileHelp::FileHelp(int argc, char* argv[]) : Cli(argc, argv)
{
	if (argc > 2) {
		help_cmd = argv[2] ;
	}
	else {
		help_cmd = NULL ;
	}
}

/* *********************************************************************************//**
 * @brief       Display help text
 * @details     int FileHelp::action(void)
 * @details     Retrieves help information from file 'file/Help.txt' and prints
 *              information to console. If command syntax does not match any help
 *              command in the help file prints an error message.
 * @return      return IOCTL_CMD_STATUS_OK
 * *************************************************************************************/

#if 0
void FileHelp::Help()
{
	if (helpcmd.empty() == false) { 
		string f = helpcmd + "/Help.txt" ;
		if (cmdHelp((char *) f.c_str())) {
			cout << appname << " : no help available for command " << helpcmd << endl ;
			cout << f << endl;
		}
	}
}
#endif

/* *********************************************************************************//**
 * @brief	Display help text
 * @details	int Cli :: cmdHelp(const char *f)
 * @details	Open the help file passed as argument.\n
 *		The default search path is: /usr/src/tools/tools/cmdtool (HELP_DIR).\n
 *		Prints all lines between the tags: \"CMD::<helpcmd>\" and \"END\"
 *		where member variable 'helpcmd' contains the command string to seach for.\n
 *		The default command string is \"help\".
 * @param [in]  f : help file string
 * @return	IOCTL_CMD_STATUS_OK or IOCTL_CMD_STATUS_ERROR
 * *************************************************************************************/
int FileHelp::cmdHelp(char *f)
{
	for (const auto& entry : HELP_ENTRIES) {
            if (strcmp(help_cmd, entry.cmd) == 0) {
                cout << "Command: " << entry.cmd << endl;
                cout << entry.description << endl;
                return IOCTL_CMD_STATUS_OK;
            }
        }
    cout << appname << " : no help available for command " << help_cmd << endl;
	return IOCTL_CMD_STATUS_ERROR;
}

int FileHelp :: Action()
{
	if (help_cmd != NULL) {
		err = cmdHelp(help_cmd) ;
	}
	else {
		cout << appname << ": help <command>" << endl ;
	}
	return err ;
}

bool FileHelp :: isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "help") == 0) {
		return true;
	}
        return false;
}


/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
