
CMD:: dump
Print a hex dump of a binary file.

Syntax: dump -f <file> 
file      : binary file that contains frame data

END

CMD:: exec
Register access: execute a batch file that contains multiple
                 register access commands.

Syntax: exec -f <file> [-if <device>] [-v]

device    : network interface e.g. eth0.
file      : text file that contains command lines.
-v        : be verbose

Sample: ./cli exec -f myfile.txt -if eth0

This command executes a list of commands in the order as they are
listed in a batch file. Depending on the type of command some
additional output e.g. when reading a register may be displayed.
The user is free to omit the interface declaration with the option
'-if' in a particular command line of the batch file. In this case
the interface must be declared in the command line of the executing
command like in the sample above.  If an interface is declared in both
the executing command line and in the the batch file the argument in
the batch file has precedence. If the verbose option is given in the
execution command line it applies only to the first command line in 
the bacth file. The user is free to specify the -v option for each
batch line accordingly.

Contents of myfile: 
	esu -if p4p1 -wr 0x1111 0x5678 -v
	mac -if p4p1 -wr 0x2222 0x9999 
	mac -if p4p1 -rd 0x3333
	mac -dump 0x4444 3 -v
	mac -v -rd 0x5555 -v 
	esu -dump 0x6666 2

Note: please do not append trailing characters e.g. blanks or tabs
to the command line ! Command line must be terminated with a '\n'.

Generates the following output:
	IOC: cmd:0x89f1-0x2 offs:0x00001111 data:0x5678 rc:0
	0x00000000
	IOC: cmd:0x89f2-0x1 offs:0x00004444 data:0x0 rc:0
	IOC: cmd:0x89f2-0x1 offs:0x00004445 data:0x0 rc:0
	IOC: cmd:0x89f2-0x1 offs:0x00004446 data:0x0 rc:0
	IOC: cmd:0x89f2-0x1 offs:0x00005555 data:0x0 rc:0
	0x00000000
	0x00000000
END


CMD:: mac esu gicu pcie

Register access: perform single register accesses to Unimac, Ethernet Switch Unit (ESU),
Generic Interrupt Controller Unit or PCIe unit.

Syntax: mac | esu | gicu | pcie -if <device> [-v] <operation>


device    : network interface e.g. eth0.
-v        : be verbose

operation : -dump <register-offset> <number>
	    Dump consecutive registers at given offset for the given
	    number of registers.

            -rd   <register-offset>
	    Read the contents of a register at given offset.
	    
            -wr   <register-offset> <value>
	    Write the value to the register at given offset.

            -w0   <register-offset> <bit-number>
	    Wait for the register value to become a zero for the given bit number.

            -w1   <register-offset> <bit-number>
	    Wait for the register value to become a one for the given bit number.

            -or   <register-offset> <value>
	    Perform a binary or operation on the contents of the given register
	    with the specified value.

            -and  <register-offset> <value>
	    Perform a binary and operation on the contents of the given register
	    with the specified value.

Note: all register offsets and values are interpreted in the hexa-decimal or decimal
      format accordingly. This means that hexa-decimal values must be preceded by an
      '0x' unless the user does not want them to be interpreted as decimal number.

Sample: ./cli mac -if eth0 -dump 0x10 0x100
        ./cli esu -if eth0 -v -wr 0x20 0x1818
	IOC: cmd:0x89f1 offs:0x00000020 data:0x1818 rc:0

END

CMD:: load
LOAD GENERATOR: load packet data from binary file into a descriptor ring.

Syntax: load -f <file> -if <device> [-l <len>] [-o <offset>] [-c <channel>] [-n <count>]

The load generator may be used to generate transmit traffic from a single packet
or multiple packets each specified in a given binary file.
The file may be created with wireshark for example. For unknown frame format it
is recommended to use the -dump command first for checking checking the contents
of the file. If there exists an offset to the beginning of the Ethernet frame
within the file data it is recommended to use the -o argument.

file      : binary file that contains an Ethernet packet.
device    : network interface e.g. eth0.
len       : number of bytes to be loaded from the file [default 128 or less].
offset    : skip number of bytes from the beginning of the file [default 0].
channel   : transmit channel index to be used beginning at zero [default 0].
count     : the number of descriptors in the given transmit channel where the
            packet shall be placed to [default 1].
	    If the value exceeds the number of allocated channel descriptors the
	    command will be rejected with an error. It is recommended to specify
	    the exact number of descriptors for generating permanent network load.

Note: be aware that if the channel descriptors are not completely filled the transmit
      operation terminates on the last descriptor automatically that is given to the
      hardware.

Note: When any channel shall be refilled with new packet data the 'lgen -tx_reset' command
      should be executed before. Otherwise data will be appended to the descriptor ring
      or existing packet data respectively. Alternatively the command 'lgen -init' may be
      used to re-initialize all channels.

Sample: ./cli load -f frame.pcap -if p4p1 -l 100 -o 0x10 -c 16 -n 72

The sample above may cause an output as follows assumed that the input file contains
at least 72+16 bytes:

len=100 offs=0 txr=0 cnt=72 err=0 completion code=0
len=68 offs=32 txr=0 cnt=72 err=0 completion code=0
len=36 offs=64 txr=0 cnt=72 err=0 completion code=0
len=4 offs=96 txr=0 cnt=72 err=0 completion code=0

100 bytes read

Note: The contents of the file is loaded in multiple chunks. Each load operation is
      limited by an internal buffer space limitation of this CLI.

END

CMD:: upload
LOAD GENERATOR: upload packet data from channel descriptor to binary file

Syntax: upload -if <device> [-f <file>] [-c <channel>] [-i <index>] [-l <len>] [-o <offset>] [-n <count>] [-v]

device    : network interface e.g. eth0.
file      : binary file to write data to
channel   : receive channel index to be requested [default 0].
index     : the first descriptor index where to request packet data from [default 0].
len       : total number of bytes to be read [default 128 or less].
offset    : data offset into receive buffer applies to the first desciptor [default 0].
count     : the number of descriptors in the given receive channel to be requested in a loop.
	    If the value exceeds the number of allocated channel descriptors the invalid
	    request will be rejected with an error.
-v        : be verbose e.g. generate a hex-dump for received data.

Sample: ./cli upload -if p4p1 -c 0 -i 0 -o 0x10 -n 5 -l 48 -v -f myfile

Description: Once any packet or part of a packet has been received on any channel the data can be
uploaded to a binary file or just can be displayed to the console as a hex-dump.
The status of the descripor nonrelevant that means that an upload operation can be done at any
time even if the descriptor elements is in use by the hardware. It is up to the responsibility of
the user to stop any (load generator) traffic. Data can be retrieved even during normal driver
operation in network mode. The driver returns the packet data only if the status information of
the receive descriptor provides a valid packet length after packet reception from the wire.
The user can specifiy an offset into packet data. The driver skips the data at the beginning of
the first descriptor ring. Subsequent descriptor data in the same request will be copied from the
beginning of the descriptor's data buffer. The argument '-l <len>' specifies the maximal data
length that is to be uploaded from a single or multiple consecutive descriptors.
If data from multiple descriptors shall be uploaded the argument '-n <count>' has to be used.

END

CMD:: lgen init tx_start tx_stop rx_start rx_stop release

LOAD GENERATOR: start / stop load generator

Syntax: lgen <command> -if <device> [-c <channel>]  [-n <count>] 

command:
-init     : enable load generator and disable network interface
-release  : disable load generator and re-enable network interface

-tx_reset : reset a single transmit channel [default channel is 0].
-tx_start : start transmit operation on a particular channel
-tx_stop  : stop transmit operation on a particular channel

-rx_reset : reset a single receive channel [default channel is 0].
-rx_start : start receive operation on a particular channel
-rx_stop  : stop receive operation on a particular channel

device    : network interface e.g. eth0.
channel   : transmit / receive channel index to be used [default 0].
	    If a channel index is given that is equal to or greater than the number
            of confgiured transmit channels (default 10) all channels will be affected.
count     : number of packets to be processed.
	    For transmit operation the number of packets may be limited if it is less or
	    equal to the number of activated descriptors per queue. If the transmit ring
	    is completely filled with frames the number of requested frames will be sent.
	    If the transmnit ring is not completely filled with transmit frames the number
	    of frames to be sent is maximal the number of available frames per transmit ring.
	    For receive operation the number of received packets is limited to the given count
	    provided that the receive ring has been completely filled with receive buffers.
	    Receive queues are totally filled with receive buffers.

Note :	arguments channel and count are not required for the '-init' and '-release' commands.
	The load generator must be enabled prior to all commands listed above.

	When returning to normal network operation the '-release' command must be executed
	before.
END

CMD:: showc

CHANNEL STATUS: show rx/tx channel descriptor status

Syntax: showc -if <device> -rx|-tx <channel>  [-o <offset>] [-n <count>] [-v]

command:
-rx       : display receive channel status
-tx       : display transmit channel status
-rb       : display receive buffer pool address information 
-v        : be verbose

device    : network interface e.g. eth0.
channel   : transmit / receive channel index to be used [default 0].
	    If a channel index is given that is equal to or greater than the number
            of confgiured transmit channels (default 10) all channels will be affected.
offset    : start at descriptor index [default 0].
count     : number of descriptors beginning at offset [default 0].

Samples of output description:

- A single line of general channel information is displayed first for transmit and
  receive descriptors::

General channel information:

   Flags Ring-Size Pending W-Idx R-Idx R-Len
       0       128      23    23     0     0 

Flags    : Channel operational bits:
Ring-Size: Number of allocated descriptors for that particular channel
Pending  : Number of transmit descriptors being processed by HW
W-Idx    : Current CPU write index to next transmit descriptor
R-Idx    : Current HW read position to be processed on next interrupt
R-Len    : Current number of desciptors in receive queue to be processed by CPU

- For each transmit descriptor a line of information is displayed.

#### BCount F L     CS-3     CS-4 Tim TimeStamp  Addr-Hi  Addr-Lo Pos
---------------------------------------------------------------------
0000 <USER> <GROUP> 1 00000000 00000000 000 000000000 00000001 1fa05002 R 
0001 000078 1 1 00000000 00000001 000 000000000 00000000 c29c0202  
0003 000000 0 0 00000000 00000000 000 000000000 00000000 00000000  W
....

####     : Start descriptor index 
BCount   : Byte count of attached buffer to be processed by HW
F / L    : If set to 1 indicates first / last descriptor of a packet.
CS-3     : Checksum level 3 computation by HW is requested.
CS-4     : Checksum level 4 computation by HW is requested.
TimeStamp: Timestamp value set by CPU.
Addr-Hi  : High 32 bit physical address of attached data buffer
Addr-Lo  : Low 32 bit physiacal addess of attached data buffer
Pos      : R indicates current HW read position.
	   W indicates current SW write position.

- For each receive descriptor a line of status information is displayed (-rx).

#### BCount F L ES1 ES2 I4 L4 L4P L3I4 L3I6 L2P Vlan TimeStamp  Rx-Csum  UdpCsum Pos
------------------------------------------------------------------------------------
0000 <USER> <GROUP> 0 000 000 00 00 000 0000 0000 000 0000 000000000 00000000 00000000  R 
....
0064 000000 0 0 000 000 00 00 000 0000 0000 000 0000 000000000 00000000 00000000   W

####     : Start descriptor index 
BCount   : Last byte count of received data in buffer 
F / L    : If set to 1 indicates first / last descriptor of a packet.
ES1      : 1 bit error summary, set to 1 on error
ES2      : 2 bit error cause
I4       : When set to 1 indicates that IPv4 header checksum is OK.
L4       : When set to 1 indicates that layer 4 TCP or UDP checksum is OK.
L4P      : Layer 4 encapsulation and protocol 2 bit identification as:
           0: other frame or unrecognized format.
	   1: Frame is TCP/IP over Ethernet V2 or LLC/SNAP (w or w/o VLAN tag)
	   2: Frame is UDP/IP over Ethernet V2 or LLC/SNAP (w or w/o VLAN tag)
	   3: reserved
L3I4     : When set to 1 indicates that frames is classified as IPv4 frame
L3I6     : When set to 1 indicates that frames is classified as IPv6 frame
L2P      : 2 bit layer 2 protocol information as:
	   0 unrecognized type
	   1 Ethernet V2 frame
	   2 LLC/SNAP frame
	   3 reserved
Vlan     : VLAN tag determined by HW
TimeStamp: Timestamp value set by HW.
Rx-Csum  : Receive checksum computed by HW.
Pos      : R indicates current SW read position.
	   W indicates current SW write position.

- For each receive descriptor a line of buffer information is displayed (-rb).

####  Addr-Hi  Addr-Lo Pos
--------------------------
0000 <USER> <GROUP> R 
0001 00000000 c29c0202  
....
0064 00000000 00000000  W

####     : Start descriptor index 
Addr-Hi  : High 32 bit physical address of buffer pool entry
Addr-Lo  : Low 32 bit physiacal addess of buffer pool entry
Pos      : R indicates current SW read position.
	   W indicates current SW write position.

END

CMD:: irq

CHANNEL STATUS: show channel specific interrupt distribution

Syntax: irq -if <device> 

device    : network interface e.g. eth0.

Samples of output description:

Each output line represents a MSI interrupt source. The row 'Vect' shows the allocated MSI
vector. Each line contains indicators (*) for all configured channels for the 5 interrupt
sources Tx (transmit interrupt), Te (transmit error), Rx (receive interrupt), Re (receive error)
and Gi (general interrupt). If the '*' is set the interrupt source is bound to the corresponding
interrupt group number (MSI#).

MSI# Vect Channel:  0     Channel:  1     Channel:  2     Channel:  3     
          Tx Te Rx Re Gi  Tx Te Rx Re Gi  Tx Te Rx Re Gi  Tx Te Rx Re Gi  
========================================================================
   0   31  *  *  *  *                                                     
   1   32                  *  *  *  *                                     
   2   33                                  *  *  *  *                     
   3   34              *               *               *   *  *  *  *  * 

END

CMD:: set

Specify a class A/B  bandwidth on outgoing Unimac queue 1 to 9.

Syntax: set -if <device> -t <channel> <-Gb,-Mb,-Kb> <-A,-B> <value> [-v]

device    : network interface e.g. eth0.
channel   : transmit queue index from 1 to 9 (0 is default queue)
-v        : be verbose

operation:	-A		Set bandwidth class A
		-B		Set bandwidth class B
		-Gb		Bandwidth given in GigaBit
		-Mb		Bandwidth given in MegaBit
		-Kb		Bandwidth given in KiloBit

To disable previously set bandwith on a transmit queue just specify a
bandwidth of 0.

END

CMD:: rxmap
Syntax: rxmap -if <device> -c <channel> [-v] <operation> <value> [-off]
Syntax: rxmap -if <device> -c <channel> [-v] -clear

device    : network interface e.g. eth0.
channel   : queue index from 1 to 9 (0 is default queue)
-v        : be verbose
-off      : disable option (default enable)

operation : -mgmt <1|0>		Match managment bit 
	    -qpri <0...7>	Match queue priority
	    -spid <0...15>	Match SPID header field
	    -flow <0...15>	Match flow id header field
	    -DA <mac address>	Match destination mac address
	    -ET <etype> <vtag>	Match ether type and vtag fields
	    -fid <fid>		Match forwarding id value
	    -off		Disable particular rx mapping
	    -clear		Clear all rx mappings per channel
END

CMD:: ATU
Syntax: ATU -if <device> <operation> [para ...]
device    :  network interface e.g. eth0.

operation : -add <mac-address> <entry> <vector> [<fid>] [qpri] [fpri]
				Add MAC address to ATU table of format xx:xx:xx:xx:xx:xx
				If fid is given put MAC address to particular FID database

				entry : entry state 0-15 , default 0xF
				vector: port vector, default 0x7FF
				fid   : database number, default 0
				qpri  : QPRI override value
				fpri  : FPRI override value

	    -flush [<fid>]	Flus all MAC address entries of a particular FID database if specified
	    -clear [<fid>]	Flus all non static MAC address entries of a particular FID database if specified
	    -list [<fid>]	List all ATU entries for a particular FID (default 0)
	    -scan 		List all ATU entries for a all FIDs (time intensive)
END

CMD:: VTU
Syntax: VTU -if <device> <operation> [para ...]
device    :  network interface e.g. eth0.

operation : -add <vid> [ <fid> <port-member> <qpri> <fpri> ]
				Add MAC address to ATU table of format xx:xx:xx:xx:xx:xx
				If fid is given put MAC address to particular FID database

				vid        : VLAN id
				fid        : FID number (default 0)
				port-member: port member ship code per port 11-0: (1 nibble / port) 
					     Sample: member ship 2 for ports 1 and 11: 0x200000000020
					     Default is 0x33333333333 (no member on all ports)
				qpri  : QPRI override value (default: disabled)
				fpri  : FPRI override value (default: disabled)

	    -flush 		Flush all VTU entries
	    -clear [<fid>]	Flush all VTU entries of a particular FID database
	    -list 		List all VTU entries 
END

CMD:: TCAM
Syntax: TCAM -if <device> <operation> [para ...]
device    :  network interface e.g. eth0.

operation : -add <entry> <SPV> <mode> [-A] [-D <file>]
				Add new 48 byte entry to switch TCAM memory.

				entry   : TCAM slot entry range 0 ... 255
				SPV	: source port vector, one bit per port, max. 0xFFF.
				mode	: TCAM mode (1,2,3) to be used for the ports in SPV
				A       : followed by action list: string=value [string=value] ...
				D <file>: specifies file name with input filter settings
					  (see file format below)

	    -flush 		Flush all TCAM entries
	    -list 		List all TCAM entries 
	    -actions		List all TCAM actions to be used as parameter

File format (-D option):
The file format is to be used for specifying the filter specific bits for data and data mask.
There is a maximum of 48 bytes that can be specified in the following format:

0000: 00 11 22 33 44 99 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0000: FF fF ff FF ff FF 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00

The first line contains the data beginning at the given offset. The second line specifies the data mask
settings. Here the same offset must be specified as in the first line. Additional lines may follow in
the the same way. Offset and data length must not exceed the 48 byte.
TCAM boundary.
END

CMD:: ESTAT
Display Ethernet Switch Unit statistics.
Syntax: ESTAT -if <device> <operation> [para ...]

operation : -show [<port>]	show statistics for all ports or the given port index
            -flush [<port>]	flush statistics for all ports or the given port index

END

CMD:: QBV
CMD:: qbv
Set up ethernet switch QBV registers and tables
Syntax 1: QBV -if <device> -dev <port> [ -set <table-set> -te <entry> | -gb <entry> ] -v <operation>
          QBV -if <device> -dev <port> -list <table-set> 
	  QBV -if <device> -dev <port> [ -set <table-set> -te <entry> | -gb <entry> ] -v <operation>
          QBV -if <device> -dev <port> -test
          QBV -if <device> -dev <port> -pe=<1,0>

Syntax 2: qbv -if <device> -dev <port> [ -set <table-set> -te <entry> | -gb <entry> ] -v -A <field>=<value> ....
          qbv -if <device> -dev <port> -list

operation : -rd [<register>]		read register
            -wr [<register> <value>]	write register
	    -v  be verbose

device    : network interface e.g. eth0.
port      : switch port number or 0x1F for global parameters
table-set : table set number 1 or 2
entry     : table entry 0-15
field     : a certain bit field of the QBV register specification
value     : any hexadecimal (0x) or decimal register value

Syntax 1:
Performs read or write register operations to a port specific QBV register offset executed
with -rd or -wr operation. If a table set is given the table entry (-te) or the guard band
entry (-gb) is read/write from/to the port's QBV  table entry.

A table write operation is executed after a specific register has been written and a table
read operation is executed before a specific read register operation is executed.
The list command reads all corresponding QBV values and prints them to the console.

The '-test' command is used to verify PreEmption support of the port's peer MAC.
The '-pe' command is used to set PreEmption support on the port's receive MAC.

Syntax 2:
Set specific QBV parameters (-A) and write them to the Ethernet Switch Unit.
If a table-set is given the corresponding guard band or table entries will be set on the HW.
The list command prints all QBV register fields to be set with the -A command

Sample: ./cli qbv -if eth0 -v -A TimeSet=1
	Sets the global bit field TimeSet and updates the corresponding global register.

Sample: ./cli qbv -if eth0 -dev 0 -v  -A PortDelay1=1 QueueState=1
	Sets the port (-dev) bit field PortDelay1 and updates the corresponding port register,
	but does not update any port table with parameter QueueState since the -set option is
	not given.

Sample: ./cli qbv -if eth0 -dev 0 -v -set 1 -te 3 -A PortDelay1=1 QueueState=1
	Sets the port (-dev) bit field PortDelay1 and updates the corresponding port register.
	Updates the port table entry 3 of set 1 with parameter QueueState.

Sample: ./cli qbv -if eth0 -dev 10 -gb -set 2 -v -A QueueState=7 GBBytes=256
	Sets the port (-dev) QueueState and GBBytes (guard bandbytes) fields and updates the
	port guard band entry of set 2 accordingly.

Sample: ./cli  QBV -if eth0 -dev 0 -list 
	Lists all port specific parameters, all port specific table sets (1,2) and all global
	parameters.
END

