/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef FILE_HELP_H
#define FILE_HELP_H

#define HELP_DIR 	"./help"
#define HELP_FILE	"Help.txt"

class FileHelp: public Cli
{
	public:

		FileHelp(int argc, char* argv[]) ;

	        static  bool            isResponsible(int argc, char* argv[]) ;

		virtual int Action(void) ;

	private:
		struct HelpEntry {
			const char* cmd;
			const char* description;
		};

		static const HelpEntry HELP_ENTRIES[];
		int	cmdHelp(char *);

		char	*help_cmd ;
		string	helpcmd ;
};

#endif // FILE_HELP_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
