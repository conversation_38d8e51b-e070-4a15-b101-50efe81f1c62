namespace HelpTexts {
    constexpr char DUMP_HELP[] = R"(
Print a hex dump of a binary file.

Syntax: dump -f <file> 
file      : binary file that contains frame data
)";

    constexpr char EXEC_HELP[] = R"(
Register access: execute a batch file that contains multiple
                 register access commands.

Syntax: exec -f <file> [-if <device>] [-v]

device    : network interface e.g. eth0.
file      : text file that contains command lines.
-v        : be verbose

Sample: autoeth_cli exec -f myfile.txt -if eth0

This command executes a list of commands in the order as they are
listed in a batch file. Depending on the type of command some
additional output e.g. when reading a register may be displayed.
The user is free to omit the interface declaration with the option
'-if' in a particular command line of the batch file. In this case
the interface must be declared in the command line of the executing
command like in the sample above.  If an interface is declared in both
the executing command line and in the the batch file the argument in
the batch file has precedence. If the verbose option is given in the
execution command line it applies only to the first command line in 
the bacth file. The user is free to specify the -v option for each
batch line accordingly.

Contents of myfile: 
	esu -if p4p1 -wr 0x1111 0x5678 -v
	mac -if p4p1 -wr 0x2222 0x9999 
	mac -if p4p1 -rd 0x3333
	mac -dump 0x4444 3 -v
	mac -v -rd 0x5555 -v 
	esu -dump 0x6666 2

Note: please do not append trailing characters e.g. blanks or tabs
to the command line ! Command line must be terminated with a '\n'.
Generates the following output:
	IOC: cmd:0x89f1-0x2 offs:0x00001111 data:0x5678 rc:0
	0x00000000
	IOC: cmd:0x89f2-0x1 offs:0x00004444 data:0x0 rc:0
	IOC: cmd:0x89f2-0x1 offs:0x00004445 data:0x0 rc:0
	IOC: cmd:0x89f2-0x1 offs:0x00004446 data:0x0 rc:0
	IOC: cmd:0x89f2-0x1 offs:0x00005555 data:0x0 rc:0
	0x00000000
	0x00000000
END
)";
    constexpr char ESU_HELP[] = R"(
CMD:: mac esu gicu pcie

Register access: perform single register accesses to Unimac, Ethernet Switch Unit (ESU),
Generic Interrupt Controller Unit or PCIe unit.

Syntax: mac | esu | gicu | pcie -if <device> [-v] <operation>


device    : network interface e.g. eth0.
-v        : be verbose

operation : -dump <register-offset> <number>
	    Dump consecutive registers at given offset for the given
	    number of registers.

            -rd   <register-offset>
	    Read the contents of a register at given offset.
	    
            -wr   <register-offset> <value>
	    Write the value to the register at given offset.

            -w0   <register-offset> <bit-number>
	    Wait for the register value to become a zero for the given bit number.

            -w1   <register-offset> <bit-number>
	    Wait for the register value to become a one for the given bit number.

            -or   <register-offset> <value>
	    Perform a binary or operation on the contents of the given register
	    with the specified value.

            -and  <register-offset> <value>
	    Perform a binary and operation on the contents of the given register
	    with the specified value.

Note: all register offsets and values are interpreted in the hexa-decimal or decimal
      format accordingly. This means that hexa-decimal values must be preceded by an
      '0x' unless the user does not want them to be interpreted as decimal number.

Sample: autoeth_cli mac -if eth0 -dump 0x10 0x100
        autoeth_cli esu -if eth0 -v -wr 0x20 0x1818
	IOC: cmd:0x89f1 offs:0x00000020 data:0x1818 rc:0

END
)";


    constexpr char LOAD_HELP[] = R"(
LOAD GENERATOR: load packet data from binary file into a descriptor ring.

Syntax: load -f <file> -if <device> [-l <len>] [-o <offset>] [-c <channel>] [-n <count>]

The load generator may be used to generate transmit traffic from a single packet
or multiple packets each specified in a given binary file.
The file may be created with wireshark for example. For unknown frame format it
is recommended to use the -dump command first for checking checking the contents
of the file. If there exists an offset to the beginning of the Ethernet frame
within the file data it is recommended to use the -o argument.

file      : binary file that contains an Ethernet packet.
device    : network interface e.g. eth0.
len       : number of bytes to be loaded from the file [default 128 or less].
offset    : skip number of bytes from the beginning of the file [default 0].
channel   : transmit channel index to be used beginning at zero [default 0].
count     : the number of descriptors in the given transmit channel where the
            packet shall be placed to [default 1].
	    If the value exceeds the number of allocated channel descriptors the
	    command will be rejected with an error. It is recommended to specify
	    the exact number of descriptors for generating permanent network load.

Note: be aware that if the channel descriptors are not completely filled the transmit
      operation terminates on the last descriptor automatically that is given to the
      hardware.

Note: When any channel shall be refilled with new packet data the 'lgen -tx_reset' command
      should be executed before. Otherwise data will be appended to the descriptor ring
      or existing packet data respectively. Alternatively the command 'lgen -init' may be
      used to re-initialize all channels.

Sample: autoeth_cli load -f frame.pcap -if p4p1 -l 100 -o 0x10 -c 16 -n 72

The sample above may cause an output as follows assumed that the input file contains
at least 72+16 bytes:

len=100 offs=0 txr=0 cnt=72 err=0 completion code=0
len=68 offs=32 txr=0 cnt=72 err=0 completion code=0
len=36 offs=64 txr=0 cnt=72 err=0 completion code=0
len=4 offs=96 txr=0 cnt=72 err=0 completion code=0

100 bytes read

Note: The contents of the file is loaded in multiple chunks. Each load operation is
      limited by an internal buffer space limitation of this CLI.

END
)";

    constexpr char LGEN_HELP[] = R"(
LOAD GENERATOR: start / stop load generator

Syntax: lgen <command> -if <device> [-c <channel>]  [-n <count>] 

command:
-init     : enable load generator and disable network interface
-release  : disable load generator and re-enable network interface

-tx_reset : reset a single transmit channel [default channel is 0].
-tx_start : start transmit operation on a particular channel
-tx_stop  : stop transmit operation on a particular channel

-rx_reset : reset a single receive channel [default channel is 0].
-rx_start : start receive operation on a particular channel
-rx_stop  : stop receive operation on a particular channel

device    : network interface e.g. eth0.
channel   : transmit / receive channel index to be used [default 0].
	    If a channel index is given that is equal to or greater than the number
            of confgiured transmit channels (default 10) all channels will be affected.
count     : number of packets to be processed.
	    For transmit operation the number of packets may be limited if it is less or
	    equal to the number of activated descriptors per queue. If the transmit ring
	    is completely filled with frames the number of requested frames will be sent.
	    If the transmnit ring is not completely filled with transmit frames the number
	    of frames to be sent is maximal the number of available frames per transmit ring.
	    For receive operation the number of received packets is limited to the given count
	    provided that the receive ring has been completely filled with receive buffers.
	    Receive queues are totally filled with receive buffers.

Note :	arguments channel and count are not required for the '-init' and '-release' commands.
	The load generator must be enabled prior to all commands listed above.

	When returning to normal network operation the '-release' command must be executed
	before.
)";

    constexpr char MAC_HELP[] = R"(
Register access: perform single register accesses to Unimac, Ethernet Switch Unit (ESU),
Generic Interrupt Controller Unit or PCIe unit.

Syntax: mac | esu | gicu | pcie -if <device> [-v] <operation>

device    : network interface e.g. eth0.
-v        : be verbose

operation : -dump <register-offset> <number>
	    Dump consecutive registers at given offset for the given
	    number of registers.

            -rd   <register-offset>
	    Read the contents of a register at given offset.
	    
            -wr   <register-offset> <value>
	    Write the value to the register at given offset.

            -w0   <register-offset> <bit-number>
	    Wait for the register value to become a zero for the given bit number.

            -w1   <register-offset> <bit-number>
	    Wait for the register value to become a one for the given bit number.

            -or   <register-offset> <value>
	    Perform a binary or operation on the contents of the given register
	    with the specified value.

            -and  <register-offset> <value>
	    Perform a binary and operation on the contents of the given register
	    with the specified value.

Note: all register offsets and values are interpreted in the hexa-decimal or decimal
      format accordingly. This means that hexa-decimal values must be preceded by an
      '0x' unless the user does not want them to be interpreted as decimal number.

Sample: autoeth_cli mac -if eth0 -dump 0x10 0x100
        autoeth_cli esu -if eth0 -v -wr 0x20 0x1818
)";

    constexpr char SHOWC_HELP[] = R"(
CHANNEL STATUS: show rx/tx channel descriptor status

Syntax: showc -if <device> -rx|-tx <channel>  [-o <offset>] [-n <count>] [-v]

command:
-rx       : display receive channel status
-tx       : display transmit channel status
-rb       : display receive buffer pool address information 
-v        : be verbose

device    : network interface e.g. eth0.
channel   : transmit / receive channel index to be used [default 0].
	    If a channel index is given that is equal to or greater than the number
            of confgiured transmit channels (default 10) all channels will be affected.
offset    : start at descriptor index [default 0].
count     : number of descriptors beginning at offset [default 0].
)";

    constexpr char UPLOAD_HELP[] = R"(
LOAD GENERATOR: upload packet data from channel descriptor to binary file

Syntax: upload -if <device> [-f <file>] [-c <channel>] [-i <index>] [-l <len>] [-o <offset>] [-n <count>] [-v]

device    : network interface e.g. eth0.
file      : binary file to write data to
channel   : receive channel index to be requested [default 0].
index     : the first descriptor index where to request packet data from [default 0].
len       : total number of bytes to be read [default 128 or less].
offset    : data offset into receive buffer applies to the first desciptor [default 0].
count     : the number of descriptors in the given receive channel to be requested in a loop.
	    If the value exceeds the number of allocated channel descriptors the invalid
	    request will be rejected with an error.
-v        : be verbose e.g. generate a hex-dump for received data.

Sample: autoeth_cli upload -if p4p1 -c 0 -i 0 -o 0x10 -n 5 -l 48 -v -f myfile

Description: Once any packet or part of a packet has been received on any channel the data can be
uploaded to a binary file or just can be displayed to the console as a hex-dump.
The status of the descripor nonrelevant that means that an upload operation can be done at any
time even if the descriptor elements is in use by the hardware. It is up to the responsibility of
the user to stop any (load generator) traffic. Data can be retrieved even during normal driver
operation in network mode. The driver returns the packet data only if the status information of
the receive descriptor provides a valid packet length after packet reception from the wire.
The user can specifiy an offset into packet data. The driver skips the data at the beginning of
the first descriptor ring. Subsequent descriptor data in the same request will be copied from the
beginning of the descriptor's data buffer. The argument '-l <len>' specifies the maximal data
length that is to be uploaded from a single or multiple consecutive descriptors.
If data from multiple descriptors shall be uploaded the argument '-n <count>' has to be used.
)";

    constexpr char ATU_HELP[] = R"(
Syntax: ATU -if <device> <operation> [para ...]
device    :  network interface e.g. eth0.

operation : -add <mac-address> <entry> <vector> [<fid>] [qpri] [fpri]
				Add MAC address to ATU table of format xx:xx:xx:xx:xx:xx
				If fid is given put MAC address to particular FID database

				entry : entry state 0-15 , default 0xF
				vector: port vector, default 0x7FF
				fid   : database number, default 0
				qpri  : QPRI override value
				fpri  : FPRI override value

	    -flush [<fid>]	Flus all MAC address entries of a particular FID database if specified
	    -clear [<fid>]	Flus all non static MAC address entries of a particular FID database if specified
	    -list [<fid>]	List all ATU entries for a particular FID (default 0)
	    -scan 		List all ATU entries for a all FIDs (time intensive)
)";

    constexpr char VTU_HELP[] = R"(
Syntax: VTU -if <device> <operation> [para ...]
device    :  network interface e.g. eth0.

operation : -add <vid> [ <fid> <port-member> <qpri> <fpri> ]
				Add MAC address to ATU table of format xx:xx:xx:xx:xx:xx
				If fid is given put MAC address to particular FID database

				vid        : VLAN id
				fid        : FID number (default 0)
				port-member: port member ship code per port 11-0: (1 nibble / port) 
					     Sample: member ship 2 for ports 1 and 11: 0x200000000020
					     Default is 0x33333333333 (no member on all ports)
				qpri  : QPRI override value (default: disabled)
				fpri  : FPRI override value (default: disabled)

	    -flush 		Flush all VTU entries
	    -clear [<fid>]	Flush all VTU entries of a particular FID database
	    -list 		List all VTU entries 
)";

    constexpr char TCAM_HELP[] = R"(
Syntax: TCAM -if <device> <operation> [para ...]
device    :  network interface e.g. eth0.

operation : -add <entry> <SPV> <mode> [-A] [-D <file>]
				Add new 48 byte entry to switch TCAM memory.

				entry   : TCAM slot entry range 0 ... 255
				SPV	: source port vector, one bit per port, max. 0xFFF.
				mode	: TCAM mode (1,2,3) to be used for the ports in SPV
				A       : followed by action list: string=value [string=value] ...
				D <file>: specifies file name with input filter settings
					  (see file format below)

	    -flush 		Flush all TCAM entries
	    -list 		List all TCAM entries 
	    -actions		List all TCAM actions to be used as parameter

File format (-D option):
The file format is to be used for specifying the filter specific bits for data and data mask.
There is a maximum of 48 bytes that can be specified in the following format:

0000: 00 11 22 33 44 99 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0000: FF fF ff FF ff FF 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00

The first line contains the data beginning at the given offset. The second line specifies the data mask
settings. Here the same offset must be specified as in the first line. Additional lines may follow in
the the same way. Offset and data length must not exceed the 48 byte.
TCAM boundary.
)";

    constexpr char ESTAT_HELP[] = R"(
Display Ethernet Switch Unit statistics.
Syntax: ESTAT -if <device> <operation> [para ...]

operation : 
        -show [<port>]	show statistics for all ports or the given port index
        -flush [<port>]	flush statistics for all ports or the given port index
note : 
    Port mapping between internal switch ports and external physical ports:
        - port 0: Internal CPU port
        - port 11(0xB): Host port
        - Internal port 1-6 -> External port 1-6
        - Internal port 9 -> External port 7  
        - Internal port 10(0xA) -> External port 8
        - Internal port 7,8 are unused

example : 
        autoeth_cli ESTAT -if enp1s0 -show
        autoeth_cli ESTAT -if enp1s0 -flush
)";

    constexpr char QBV_HELP[] = R"(
Set up ethernet switch QBV registers and tables
Syntax 1: QBV -if <device> -dev <port> [ -set <table-set> -te <entry> | -gb <entry> ] -v <operation>
          QBV -if <device> -dev <port> -list <table-set> 
	  QBV -if <device> -dev <port> [ -set <table-set> -te <entry> | -gb <entry> ] -v <operation>
          QBV -if <device> -dev <port> -test
          QBV -if <device> -dev <port> -pe=<1,0>

Syntax 2: qbv -if <device> -dev <port> [ -set <table-set> -te <entry> | -gb <entry> ] -v -A <field>=<value> ....
          qbv -if <device> -dev <port> -list

operation : -rd [<register>]		read register
            -wr [<register> <value>]	write register
	    -v  be verbose

device    : network interface e.g. eth0.
port      : switch port number or 0x1F for global parameters
table-set : table set number 1 or 2
entry     : table entry 0-15
field     : a certain bit field of the QBV register specification
value     : any hexadecimal (0x) or decimal register value
)";

    constexpr char SET_HELP[] = R"(
Specify a class A/B  bandwidth on outgoing Unimac queue 1 to 9.

Syntax: set -if <device> -t <channel> <-Gb,-Mb,-Kb> <-A,-B> <value> [-v]

device    : network interface e.g. eth0.
channel   : transmit queue index from 1 to 9 (0 is default queue)
-v        : be verbose

operation:	-A		Set bandwidth class A
		-B		Set bandwidth class B
		-Gb		Bandwidth given in GigaBit
		-Mb		Bandwidth given in MegaBit
		-Kb		Bandwidth given in KiloBit

To disable previously set bandwith on a transmit queue just specify a
bandwidth of 0.
)";

    constexpr char RXMAP_HELP[] = R"(
Syntax: rxmap -if <device> -c <channel> [-v] <operation> <value> [-off]
Syntax: rxmap -if <device> -c <channel> [-v] -clear

device    : network interface e.g. eth0.
channel   : queue index from 1 to 9 (0 is default queue)
-v        : be verbose
-off      : disable option (default enable)

operation : -mgmt <1|0>		Match managment bit 
	    -qpri <0...7>	Match queue priority
	    -spid <0...15>	Match SPID header field
	    -flow <0...15>	Match flow id header field
	    -DA <mac address>	Match destination mac address
	    -ET <etype> <vtag>	Match ether type and vtag fields
	    -fid <fid>		Match forwarding id value
	    -off		Disable particular rx mapping
	    -clear		Clear all rx mappings per channel
)";

    constexpr char IRQ_HELP[] = R"(
CHANNEL STATUS: show channel specific interrupt distribution

Syntax: irq -if <device> 

device    : network interface e.g. eth0.

Samples of output description:

Each output line represents a MSI interrupt source. The row 'Vect' shows the allocated MSI
vector. Each line contains indicators (*) for all configured channels for the 5 interrupt
sources Tx (transmit interrupt), Te (transmit error), Rx (receive interrupt), Re (receive error)
and Gi (general interrupt). If the '*' is set the interrupt source is bound to the corresponding
interrupt group number (MSI#).

MSI# Vect Channel:  0     Channel:  1     Channel:  2     Channel:  3     
          Tx Te Rx Re Gi  Tx Te Rx Re Gi  Tx Te Rx Re Gi  Tx Te Rx Re Gi  
========================================================================
   0   31  *  *  *  *                                                     
   1   32                  *  *  *  *                                     
)";

    constexpr char PHY2112_HELP[] = R"(
    CMD:: phy2112
设置phy的主从速率及其他配置
Syntax: phy -if <device> <operation> [para ...]

operation : -set   <port>	MasterSlave=<value> LinkSpeed=<value>
            -get   <port>
            -init [<port>]
            -diag  <port>
sample: 

    autoeth_cli phy2112 -if enp1s0 -set 1  MasterSlave=1 LinkSpeed=1  
		# 设置1号端口为 主模式千兆
    autoeth_cli phy2112 -if enp1s0 -get 1     # 读取1号端口的主从速率
    autoeth_cli phy2112 -if enp1s0 -init 1    # 初始化1号端口
    autoeth_cli phy2112 -if enp1s0 -init      # 初始化所有端口
    autoeth_cli phy2112 -if enp1s0 -diag 1    # 诊断1号端口
	 	SQI (Signal Quality Indicator)
			值范围: 0-15 (15为最佳质量)
		CQI (Cable Quality Indicator)
			IL (Insertion Loss):0-3 为优良 越低越好
			RL (Return Loss):  15以上为优良 越高越好
		VCT (Virtual Cable Test)
			distanceToFault: 表示从测试设备到电缆故障点的距离 0表示无故障
			cable_status: 
				t: test passed
				o: open
				s: short
				x: fault
				u: unknown
END
)";

    constexpr char PHY1512_HELP[] = R"(
    CMD:: phy1512
	初始化1512phy芯片
Syntax: phy1512 -if enp1s0 -init
Sample: autoeth_cli phy1512 -if enp1s0 -init
END
)";

    constexpr char FORWARD_HELP[] = R"(
    CMD:: forward
设置6113芯片的转发规则
Syntax: forward -if <device> <operation> [para ...]

operation : -set PORTS=<port>,<port> HOST=<value>
            -get <port>

samples :
        autoeth_cli forward -if enp1s0 -set  PORTS=2,4 HOST=1
            # 设置2端口和4端口之间强制转发
            # 同时将数据向host端口转发
            # 相当于设置网桥

        autoeth_cli forward -if enp1s0 -set  PORTS=1 HOST=1
            # 设置1端口的强制向host端口转发
            # 相当于设置监听模式

        autoeth_cli forward -if enp1s0 -get 1
            # 读取1端口的转发规则
END
)";

}
