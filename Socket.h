/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef SOCKET_H
#define SOCKET_H

#include <sys/socket.h>
#include <sys/ioctl.h>
#include <linux/if.h>

class Socket {
	private:
		int	fd ;
		struct	ifreq ifr ;

	public:
			Socket() ;
			~Socket() ;
		bool	SetDevice(char *dev) ;
		int	Ioctl(int cmd, caddr_t data) ;
};

#endif // SOCKET_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
