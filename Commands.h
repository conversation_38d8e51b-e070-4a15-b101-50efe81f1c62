/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef COMMANDS_H
#define COMMANDS_H

// Do not touch these definitions
//
#ifdef	SKIP_ARGUMENT
#define SKIP_ARG(n)     argv[(n)]=argv[0];argv+=(n);argc-=(n)
#else
#define SKIP_ARG(n)
#endif

// Macro is used in <module-dir>/Command.h
//
#define CMD_TABLE(TYPE) \
        if (TYPE::isResponsible(argc, argv)) return new TYPE(argc, argv)

// #################################################################
// ##            ADD COMMAND TABLE INCLUDE FILES HERE             ##
// #################################################################
//
#include "register/Commands.h"
#include "help/Commands.h"
#include "misc/Commands.h"
#include "esu/Commands.h"


#define	CMDTOOL_CMD(MODULE,CMDTAB, sl, sf) \
	if (argc > 1 && strcmp(argv[1],MODULE)==0) { sf=MODULE; SKIP_ARG(1); CMDTAB ; } \
	else { sl += MODULE" " ; }

// #################################################################
// ##               ENTER NEW COMMAND TABLES HERE                 ##
// #################################################################
//
// Add <module>_COMMAND_TABLE to the macro below. The command table
// is defined in the module specific file: <module-dir>/Command.h
// (do not forget the 'else' at end of the line unless appended to
//  the end)
//
#define	CMDTOOL_CMD_TABLE(sl,sf)	\
	CMDTOOL_CMD("help", HELP_COMMAND_TABLE, sl, sf) ;    \
	CMDTOOL_CMD("mac",  REGISTER_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("esu",  REGISTER_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("gicu", REGISTER_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("pcie", REGISTER_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("gen", REGISTER_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("exec", REGISTER_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("rxmap", REGISTER_COMMAND_TABLE, sl, sf) ; \
\
	CMDTOOL_CMD("load",  FILE_COMMAND_TABLE, sl, sf) ; \
	CMDTOOL_CMD("upload",FILE_COMMAND_TABLE, sl, sf) ; \
	CMDTOOL_CMD("lgen",FILE_COMMAND_TABLE, sl, sf) ; \
	CMDTOOL_CMD("irq",  FILE_COMMAND_TABLE, sl, sf) ; \
	CMDTOOL_CMD("showc", FILE_COMMAND_TABLE, sl, sf) ; \
	CMDTOOL_CMD("dump", FILE_COMMAND_TABLE, sl, sf) ; \
	CMDTOOL_CMD("set",  FILE_COMMAND_TABLE, sl, sf) ;\
\
	CMDTOOL_CMD("PHY2112",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("PHY1512",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("FORWARD",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("phy2112",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("phy1512",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("forward",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("ATU",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("VTU",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("TCAM", ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("ESTAT",ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("QBV",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("qbv",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("TAI",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("PTP_GLOBAL",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("ptp_global",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("PTP_PORT",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("ptp_port",  ESU_COMMAND_TABLE, sl, sf) ;\
	CMDTOOL_CMD("ahsi",  REGISTER_COMMAND_TABLE, sl, sf) ;

// #################################################################
// ##                   ENTER NEW MODULES HERE                    ##
// #################################################################
//
// Add module name (sub-directory) to the list.
// (used for help text) ...
#if 0
#define	MODULE_LIST { "register" }
#endif

#endif // COMMANDS_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
