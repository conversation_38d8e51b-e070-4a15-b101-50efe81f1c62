################################################################################
#
#       LICENSE:
#       (C)Copyright 2015 Marvell.
#
#       All Rights Reserved
#
#       THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
#       The copyright notice above does not evidence any
#       actual or intended publication of such source code.
#
#       This Module contains Proprietary Information of Marvell
#       and should be treated as Confidential.
#
#       The information in this file is provided for the exclusive use of
#       the licensees of Marvell. Such users have the right to use, modify,
#       and incorporate this code into products for purposes authorized by
#       the license agreement provided they include this notice and the
#       associated copyright notice with any such product.
#
#       The information in this file is provided "AS IS" without warranty.
#       /LICENSE
#
################################################################################

# Use DEBUG for additional information outpout
#
# Use DEBSIM if compiled in standalone mode without having a real 
# interface to the HAL layer
#
# compile standalone with main

DEBUG   = -DDEBUG -g -O0 -Wall -Wextra
OS      = -DLINUX

DEF   = $(DEBUG) $(OS) 

CC      = gcc
CPP     = g++ 
LINT	= splint

REG_DIR = register/
ESU_DIR = esu/
HLP_DIR = help/
FIL_DIR = misc/
DRV_DIR = ../

REG_OBJ =  RegisterIO.o RegisterFileIO.o RxFlow.o
ESU_OBJ =  Esu.o EsuAtu.o EsuVtu.o EsuTcam.o EsuStat.o EsuQbv.o EsuQbvVal.o EsuTai.o EsuPtpGlobal.o EsuPtpPort.o EsuPhy2112.o EsuForward.o EsuPhy1512.o
HLP_OBJ =  Help.o 
FIL_OBJ =  FileDump.o FileLoad.o FileUpLoad.o LoadGenerator.o Channel.o MsiIrq.o Set.o

LOCINC  = -I. -I$(DRV_DIR) -I$(REG_DIR) -I$(ESU_DIR) -I$(HLP_DIR) -I$(FIL_DIR)

CFLAGS =  $(DEF) $(SYSINC) $(LOCINC) -fPIC
CPPFLAGS= $(CFLAGS)
CXXFLAGS= $(CFLAGS)

TARGET = autoeth_cli
MYLIBS = 

SUBOBJECTS = $(addprefix $(HLP_DIR),$(HLP_OBJ))
SUBOBJECTS += $(addprefix $(REG_DIR),$(REG_OBJ))
SUBOBJECTS += $(addprefix $(ESU_DIR),$(ESU_OBJ))
SUBOBJECTS += $(addprefix $(FIL_DIR),$(FIL_OBJ))

# Objects for windows
#

# The objects below are at least needed to compile the CLI module
#
OBJS = Cli.o CliFactory.o Main.o Socket.o File.o
OBJS += $(SUBOBJECTS)

# suffix rules for all kinds of files
.SUFFIXES: .c .o .cpp .cc .cxx .C

.C.o:
	$(CC) $(CFLAGS) -c -o $@ $<
.c.o:
	$(CC) $(CFLAGS) -c -o $@ $<

all:	$(TARGET)

$(TARGET): $(OBJS) 
	$(CXX) -o $@ $^ $(CXXFLAGS) $(LDFLAGS)

clean:
	rm -f *.o
	rm -f $(REG_DIR)/*.o
	rm -f $(ESU_DIR)/*.o
	rm -f $(HLP_DIR)/*.o
	rm -f $(FIL_DIR)/*.o
################################################################################
#
# End of file
#
################################################################################
