/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

/* *********************************************************************************//**
 * @defgroup CMDT_IOC	CMDT IO command processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_IOC
 * @{
 * *************************************************************************************/

#include <iomanip> // setiosflags
#include <iostream>
#include <sstream> // stringstream
#include <string> // stringstream
#include <fstream> // stringstream

#include <string.h>
#include <unistd.h>

#include "Cli.h"
#include "Socket.h"

using namespace std;

Socket :: Socket()
{
	fd = socket(AF_INET, SOCK_DGRAM, 0);
}

Socket :: ~Socket()
{
	if (fd >= 0) {
		close(fd) ;
	}
}

/* *********************************************************************************//**
 * @brief	Class contructor
 * @details	Cli :: Cli()
 * *************************************************************************************/

bool Socket :: SetDevice(char *dev)
{
	ifr.ifr_addr.sa_family = AF_INET;

	if (strlen(dev) < sizeof(ifr.ifr_name)) {
		strcpy(ifr.ifr_name,dev) ;
		return (true) ;
	}
	return (false) ;
}

int Socket :: Ioctl(int cmd, caddr_t data)
{
	int	rc = -1 ;
	if (fd != -1) {
		ifr.ifr_data = (caddr_t) data;
		rc = ioctl(fd, cmd, (caddr_t) &ifr);
		if (rc != 0) {
			rc = IOCTL_CMD_STATUS_ERROR ;
		}
	}
	else {
		rc = IOCTL_CMD_STATUS_ERROR ;
	}
	return (rc) ;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
