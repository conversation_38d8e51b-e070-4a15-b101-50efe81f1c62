PTP Port Command Examples
========================

This document provides practical examples for using the PTP_PORT command to configure and monitor PTP (Precision Time Protocol) port-specific operations.

Prerequisites:
- Network interface (e.g., eth0) must be available
- Switch ports 1-11 are valid port numbers
- Root privileges may be required for register access

Basic Operations
================

1. List all PTP port registers for port 1:
   ./autoeth_cli PTP_PORT -if eth0 -port 1 -list

2. Read a specific register (e.g., Transport Specification register 0x00):
   ./autoeth_cli PTP_PORT -if eth0 -port 1 -rd 0x00

3. Write to a register (e.g., set arrival mode in register 0x02):
   ./autoeth_cli PTP_PORT -if eth0 -port 1 -wr 0x02 0x1FF

4. Enable verbose mode for detailed output:
   ./autoeth_cli PTP_PORT -if eth0 -port 1 -list -v

PTP Port Configuration
======================

5. Configure PTP port for Ethernet transport with basic message types:
   # transSpec=0 (Ethernet), msgTypes=0x7 (Sync+Delay_Req+Pdelay_Req)
   # arrivalMode=0x7 (capture all), ledControl=0x0101 (basic LED)
   ./autoeth_cli PTP_PORT -if eth0 -port 2 -config 0 0x7 0x7 0x0101

6. Configure for IPv4/UDP transport:
   # transSpec=1 (IPv4/UDP), msgTypes=0xF (more message types)
   ./autoeth_cli PTP_PORT -if eth0 -port 3 -config 1 0xF 0xF 0x0202

7. Configure for IPv6/UDP transport with full message type support:
   # transSpec=2 (IPv6/UDP), msgTypes=0x1FF (all message types)
   ./autoeth_cli PTP_PORT -if eth0 -port 4 -config 2 0x1FF 0x1FF 0x0303

Timestamp Format Configuration
==============================

8. Set timestamp format to ToD (32-bit ToD format):
   ./autoeth_cli PTP_PORT -if eth0 -port 1 -tsformat 1

9. Set timestamp format to global timer (32-bit global timer):
   ./autoeth_cli PTP_PORT -if eth0 -port 1 -tsformat 0

10. Get current timestamp format:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -gettsformat

Timestamp Operations
====================

11. Get arrival timestamp from time index 0:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -arrival 0

12. Get arrival timestamp from time index 1:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -arrival 1

10. Get departure timestamp:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -departure

11. Check PTP port status (arrival/departure interrupt flags):
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -status

Path Delay Configuration
========================

12. Set basic path delay compensation:
    # ingressMean=1000, ingressAsymm=50, egressAsymm=25
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -delay 1000 50 25

13. Set higher path delay values for longer cables:
    # ingressMean=5000, ingressAsymm=200, egressAsymm=150
    ./autoeth_cli PTP_PORT -if eth0 -port 2 -delay 5000 200 150

14. Zero out path delay compensation:
    ./autoeth_cli PTP_PORT -if eth0 -port 3 -delay 0 0 0

Advanced Configuration
======================

15. Read configuration register using pointer mechanism:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -readcfg 0x10

16. Write configuration register using pointer mechanism:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -writecfg 0x10 0xAB

Multi-Port Configuration
========================

17. Configure multiple ports with the same settings:
    for port in {1..4}; do
        ./autoeth_cli PTP_PORT -if eth0 -port $port -config 0 0x7 0x7 0x0101
    done

18. Check status of all ports:
    for port in {1..11}; do
        echo "Port $port status:"
        ./autoeth_cli PTP_PORT -if eth0 -port $port -status
    done

Monitoring and Debugging
========================

19. Monitor arrival timestamps continuously:
    while true; do
        ./autoeth_cli PTP_PORT -if eth0 -port 1 -arrival 0 -v
        sleep 1
    done

20. Check if timestamps are being captured:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -status -v

21. Verify register configuration:
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -rd 0x00 -v  # Transport spec
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -rd 0x02 -v  # Arrival mode
    ./autoeth_cli PTP_PORT -if eth0 -port 1 -rd 0x03 -v  # LED control

Common Register Values
======================

Transport Specification (Register 0x00, bits 15:14):
- 0: Ethernet
- 1: IPv4/UDP  
- 2: IPv6/UDP
- 3: 802.3/Ethernet

Message Type Enables (Register 0x02, bits 8:0):
- Bit 0: Sync
- Bit 1: Delay_Req
- Bit 2: Pdelay_Req
- Bit 3: Pdelay_Resp
- Bit 4: Follow_Up
- Bit 5: Delay_Resp
- Bit 6: Pdelay_Resp_Follow_Up
- Bit 7: Announce
- Bit 8: Signaling

Status Flags:
- AT0/AT1: Arrival interrupt status (bit 7 in status registers)
- V0/V1: Arrival time valid (bit 6 in status registers)
- DT: Departure interrupt status (bit 7 in departure status)
- TV: Departure time valid (bit 6 in departure status)

Troubleshooting
===============

If timestamps are not captured:
1. Check port configuration: ./autoeth_cli PTP_PORT -if eth0 -port X -rd 0x00
2. Verify message type enables: ./autoeth_cli PTP_PORT -if eth0 -port X -rd 0x02
3. Check status flags: ./autoeth_cli PTP_PORT -if eth0 -port X -status -v

If path delay compensation is not working:
1. Verify delay values: ./autoeth_cli PTP_PORT -if eth0 -port X -rd 0x1C
2. Check asymmetry settings: ./autoeth_cli PTP_PORT -if eth0 -port X -rd 0x1D
3. Verify egress asymmetry: ./autoeth_cli PTP_PORT -if eth0 -port X -rd 0x1E

Note: Replace 'X' with actual port number (1-11) and 'eth0' with your network interface.
