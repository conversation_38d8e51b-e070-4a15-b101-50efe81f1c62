/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef FILELOAD_H
#define FILELOAD_H

#include "Socket.h"
#include "FileDump.h"
#include "oak_ioc_lgen.h"

#define	MAX_NUMBER_OF_INPUT_FILES	16

class FileLoad : public FileDump, public Socket
{
	public:
		FileLoad(int argc, char* argv[]) ;
		virtual int		preAction(void) ;

		virtual void		DumpBuffer(char *p, unsigned int len, unsigned int offs) ;
		virtual CliArgs_t *     SetArgumentList(uint32_t *arg_list_size) ;
                virtual int		EvaluateArgumentList(int argc, char *argv[]) ;

		static bool		isResponsible(int argc, char* argv[]);

	protected:
		oak_ioc_lgen	ioc_data ;
		int		ioc_cmd ;
		char		*if_name ;
		uint32_t	channel ;
		uint32_t	count ;
};

#endif // FILELOAD_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
