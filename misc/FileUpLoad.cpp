/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "Cli.h"
#include "FileUpLoad.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
	{ "-f",	  1, 1 },	// file name
	{ "-if",  1, 1 },	// interface name
	{ "-l",   1, 1 },	// length od data to be uploaded
	{ "-o",   1, 1 },	// data offset to start with
	{ "-c",   1, 1 },	// upload from rx channel index
	{ "-i",   1, 1 },	// descriptor index 
	{ "-n",   1, 1 },	// number of descriptors
	{ "-v",   0, 0 },	// be verbose 
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     FileUpLoad::FileUpLoad(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

FileUpLoad::FileUpLoad(int argc, char* argv[]) : File(argc, argv), Cli(argc, argv), Socket()
{
	channel = 0 ;
	if_name = NULL ;

	ioc_cmd = OAK_IOCTL_LGEN ;
	ioc_data.cmd = OAK_IOCTL_LGEN_RX_DATA ;
	ioc_data.offs = 0 ;
	ioc_data.count = 0 ;
	ioc_data.channel = 0 ;

	count = 1 ;
	verbose = false ;
	file_name = NULL ;
}

CliArgs_t * FileUpLoad :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int FileUpLoad :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	// err = FileDump :: EvaluateArgumentList(argc,argv) ;
	if (err == IOCTL_CMD_STATUS_OK) {
                err = ScanArgList(argc, argv, "-if", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
			if_name = argv[pos] ;
		
                	err = ScanArgList(argc, argv, "-c", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				ioc_data.channel = StringToDecHexInt(argv[pos]);
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}

                	err = ScanArgList(argc, argv, "-o", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				ioc_data.offs = StringToDecHexInt(argv[pos]);
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}

			err = ScanArgList(argc, argv, "-l", &pos, &num) ;
			if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				length = StringToDecHexInt(argv[pos]);
			}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				length = sizeof(data_buf) ; // Use default 
				err = IOCTL_CMD_STATUS_OK ;
			}

			err = ScanArgList(argc, argv, "-i", &pos, &num) ;
			if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				ioc_data.count = StringToDecHexInt(argv[pos]);
			}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}

			err = ScanArgList(argc, argv, "-n", &pos, &num) ;
			if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				count = StringToDecHexInt(argv[pos]);
			}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}

			err = ScanArgList(argc, argv, "-f", &pos, &num) ;
			if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				file_name = argv[pos] ;
			}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}
	
			err = ScanArgList(argc, argv, "-v", &pos, &num) ;
			if (err == IOCTL_CMD_STATUS_OK) {
				verbose = true ;
			}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}
		}
	}
	return (err) ;
}

int FileUpLoad :: preAction()
{
	// err = FileDump::preAction() ;
	if (err == IOCTL_CMD_STATUS_OK && if_name != NULL) {
		if (SetDevice(if_name) == false) {
			err = IOCTL_CMD_STATUS_ERROR ;
		}
	}

	if (file_name != NULL) {
		err =  OpenFile(file_name,true) ;
	}
	return (err) ;
}

int FileUpLoad :: postAction()
{
	CloseFile() ;
	return (err) ;
}

int FileUpLoad :: Action(void)
{
	uint32_t	offs = 0 ;

	ioc_data.error = 0 ;

	// ioc_data.count = StringToDecHexInt(argv[pos]);

	do {
		uint32_t cpylen = length ;

		ioc_data.offs = 0 ;	 /* set offset to 0 on iteration */
		while (cpylen > 0) {
			ioc_data.len = cpylen ;

			/* Set ioc_data.val to target tx ring index 16 bit + descriptor count 16 bit */
			// ioc_data.channel = channel ;

			err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;

			if (verbose == true) {
				cout << dec << "len=" << ioc_data.len << " offs=" << ioc_data.offs << " rxr=" << ioc_data.channel << 
					" err=" << err << " completion code=" << ioc_data.error << endl ;

				if (err == IOCTL_CMD_STATUS_OK && ioc_data.error == 0 && ioc_data.len > 0) {
					DisplayPacketAsHexDump(ioc_data.data, ioc_data.len, 1, true, 16, offs) ;
					cout << endl ;
				}
				else {
					break ;
				}
			}

			if (file_name != NULL && err == IOCTL_CMD_STATUS_OK && ioc_data.error == 0) {
				WriteFileBin(ioc_data.data, ioc_data.len) ;
			}

			length -= ioc_data.len ; /* returned length of data received */
			cpylen -= ioc_data.len ;
			offs   += ioc_data.len ;
			ioc_data.offs += ioc_data.len ;
		}
		++ ioc_data.count ;
		-- count ;
	} while (err == IOCTL_CMD_STATUS_OK && ioc_data.error == 0 && length > 0 && count > 0) ;

	if (ioc_data.error != 0) {
		err = IOCTL_CMD_STATUS_ERROR ;
	}
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool FileUpLoad::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool FileUpLoad::isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "upload") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
