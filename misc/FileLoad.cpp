/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#if 0
#include "../CliFactory.h"
#endif
#include "Cli.h"
#include "FileLoad.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
	{ "-f",	  1, 1 },	// file name
	{ "-if",  1, 1 },	// interface name
	{ "-l",   1, 1 },	// length od data to be displayed
	{ "-o",   1, 1 },	// data offset to start with
	{ "-c",   1, 1 },	// load to tx channel index
	{ "-n",   1, 1 },	// number of descriptor elements
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     FileLoad::FileLoad(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

FileLoad::FileLoad(int argc, char* argv[]) : FileDump(argc, argv), Socket()
{
	channel = 0 ;
	count= 1 ;
	if_name = NULL ;

	ioc_cmd = OAK_IOCTL_LGEN ;
	ioc_data.cmd = OAK_IOCTL_LGEN_TX_DATA ;
	ioc_data.offs= 0 ;
}

CliArgs_t * FileLoad :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int FileLoad :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = FileDump :: EvaluateArgumentList(argc,argv) ;
	if (err == IOCTL_CMD_STATUS_OK) {
                err = ScanArgList(argc, argv, "-if", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
			if_name = argv[pos] ;
		
                	err = ScanArgList(argc, argv, "-c", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				channel = StringToDecHexInt(argv[pos]);
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
					return (err) ;
			}
                	err = ScanArgList(argc, argv, "-n", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				count = StringToDecHexInt(argv[pos]);
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}
		}
	}
	return (err) ;
}

int FileLoad :: preAction()
{
	err = FileDump::preAction() ;
	if (err == IOCTL_CMD_STATUS_OK && if_name != NULL) {
		if (SetDevice(if_name) == false) {
			err = IOCTL_CMD_STATUS_ERROR ;
		}
	}
	return (err) ;
}

void FileLoad :: DumpBuffer(char *p, unsigned int len, unsigned int offs)
{
	ioc_data.error = 0 ;
	do {
		unsigned int l ;

		l = (len < sizeof(ioc_data.data)) ? len : sizeof(ioc_data.data) ;
		
		bcopy(p,ioc_data.data,l) ;
		ioc_data.len = l ;

		/* Set ioc_data.val to target tx ring index 16 bit + descriptor count 16 bit */
		ioc_data.channel = channel ;
		ioc_data.count= count ;

		err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;

		cout << "len=" << len << " offs=" << ioc_data.offs << " txr=" << ioc_data.channel << " cnt=" << ioc_data.count << hex <<
			" err=" << dec << err << " completion code=" << ioc_data.error << endl ;

		p += l ;
		ioc_data.offs += l ;
		len  -= l ;

	} while (err == IOCTL_CMD_STATUS_OK && ioc_data.error == 0 && len > 0) ;
	if (ioc_data.error != 0) {
		err = IOCTL_CMD_STATUS_ERROR ;
	}
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool FileLoad::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool FileLoad::isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "load") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
