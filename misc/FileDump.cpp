/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#if 0
#include "../CliFactory.h"
#endif
#include "Cli.h"
#include "FileDump.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
	{ "-f",	  1, 1 },	// Just 1 argument == file name
	{ "-l",   1, 1 },	// Just 1 argument == length od data to be displayed
	{ "-o",   1, 1 }	// Just 1 argument == data offset to start with
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     FileDump::FileDump(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

FileDump::FileDump(int argc, char* argv[]) : File(argc, argv) , Cli(argc, argv)
{
	file_buf[0] = 0 ;
	file_size = sizeof(file_buf) ; // Use default 
	byte_read = 0 ;
	next_input_file = 0 ;
}

CliArgs_t * FileDump :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int FileDump :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-f", &pos, &num_of_input_files) ;
	if (err == IOCTL_CMD_STATUS_OK) {

		for (uint32_t i = 0 ; i < num_of_input_files; i++) {
			file_name[i] = argv[pos++] ;
		}

		err = ScanArgList(argc, argv, "-l", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			file_size = StringToDecHexInt(argv[pos]);
		}
		else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
			file_size = sizeof(file_buf) ; // Use default 
		}
		else {
			return (err) ; 	// on error
		}

		err = ScanArgList(argc, argv, "-o", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			file_offs = StringToDecHexInt(argv[pos]);
		}
		else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
			file_offs = 0 ;
			err = IOCTL_CMD_STATUS_OK ;
		}
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       File open
 * @details     int FileDump::preAction(void)
 * @details     Opens the file in read-only mode.
 * @return      returns the file descriptor
 * *************************************************************************************/

int FileDump::preAction(void)
{
	err =  OpenFile(file_name[next_input_file],false) ;
	return (err) ;
}

void FileDump::ContinueWithRead(void)
{
	file_end = false ;
	file_size = sizeof(file_buf) ;
}

bool FileDump::IsLastInputFile()
{
	if ((next_input_file + 1) < num_of_input_files) {
		return (false) ;
	}
	return (true) ;
}

bool FileDump::NextInputFile()
{
	if ((next_input_file + 1) < num_of_input_files) {
		++next_input_file ;
		return (true) ;
	}
	return (false) ;
}

/* *********************************************************************************//**
 * @brief       Read data from file
 * @details     int FileDump::action(void)
 * @details     Reads the contents of the file in a loop and prints a hex dump.
 * @return      IOCTL_CMD_STATUS_OK (ok), IOCTL_CMD_STATUS_ERROR (on IO failure)
 * *************************************************************************************/

int FileDump::Action(void)
{
	size_t	sum ;
	size_t	off;
	int	rlen ;
	int	offs ;


	byte_read = 0 ;
	cmd = 0;
	off = 0 ;
	sum = 0 ;
	file_end = false ;
	rlen= 0 ;

	err = IOCTL_CMD_STATUS_OK ;
	IoFile->seekg(file_offs) ;
	offs = file_offs ;

	while (!file_end && file_size && err == IOCTL_CMD_STATUS_OK) {
		if (off == 0) {
			if (file_size > sizeof(file_buf))
				rlen = sizeof(file_buf) ;
			else {
				rlen = file_size ;
				file_end = true ;
			}
		}
		rlen = ReadFileBin(&file_buf[off],rlen) ;

		if (rlen <= 0) {
			if (off != 0) {
				DumpBuffer(file_buf,sum,offs) ;
			}
			break ;
		}
		sum += rlen ;

		// Read the rest until file_buf is filled ...
		//
		if (!file_end && (sum < sizeof(file_buf))) {
			off = rlen ;
			rlen= sizeof(file_buf) - rlen ;
			byte_read += sum ;
		} 
		else {	// Display total file_buf or rest at eof
			// DisplayPacketAsHexDump(file_buf,sum,1,true,16,offs) ;
			rlen -= off ;
			file_size -= rlen ;
			off = 0 ;
			byte_read += sum ;
			DumpBuffer(file_buf,sum,offs) ;
			sum = 0 ;
			offs += rlen ;
		}
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int FileDump::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/

int FileDump::postAction(void)
{
	if (err == IOCTL_CMD_STATUS_OK) { // was initialization ok?
		cout << endl << dec << byte_read << " bytes read" << endl ;
	}
	CloseFile() ;
	return err;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool FileDump::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool FileDump::isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "dump") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
