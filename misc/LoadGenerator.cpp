/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "Cli.h"
#include "LoadGenerator.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
	{ "-init",     0, 0 },	// Initialize load generator 
	{ "-release",  0, 0 },	// Release load generator 

	{ "-tx_reset", 0, 0 },	// Reset tx channel
	{ "-tx_start", 0, 0 },	// Start command for transmitter
	{ "-tx_stop",  0, 0 },	// Stop command for transmitter

	{ "-rx_reset", 0, 0 },	// Reset rx channel
	{ "-rx_start", 0, 0 },	// Start command for receiver
	{ "-rx_stop",  0, 0 },	// Stop command for receiver

	{ "-if",    1, 1 },	// Just 1 argument == interface name
        { "-c",	    1, 1 },     // start/stop tx ring index
        { "-n",     1, 1 },     // number of iterations
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     LoadGenerator::LoadGenerator(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

LoadGenerator::LoadGenerator(int argc, char* argv[]) : Cli(argc, argv), Socket()
{
	if_name = NULL ;

	ioc_cmd = OAK_IOCTL_LGEN ;
	ioc_data.offs= 0 ;
	ioc_data.len = 0 ;
	ioc_data.channel = 0 ;
	ioc_data.count = 1 ;
}

CliArgs_t * LoadGenerator :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int LoadGenerator :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	if (err == IOCTL_CMD_STATUS_OK) {
                err = ScanArgList(argc, argv, "-if", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
			if_name = argv[pos] ;

                	err = ScanArgList(argc, argv, "-init", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_INIT ;
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}

                	err = ScanArgList(argc, argv, "-tx_reset", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_TX_RESET ;
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				return (err) ;
			}

                	err = ScanArgList(argc, argv, "-rx_reset", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_RX_RESET ;
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				return (err) ;
			}
		
                	err = ScanArgList(argc, argv, "-tx_start", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_TX_START ;
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				return (err) ;
			}
                	err = ScanArgList(argc, argv, "-tx_stop", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_TX_STOP ;
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				return (err) ;
			}

                	err = ScanArgList(argc, argv, "-rx_start", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_RX_START ;
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				return (err) ;
			}
                	err = ScanArgList(argc, argv, "-rx_stop", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_RX_STOP ;
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				return (err) ;
			}

                	err = ScanArgList(argc, argv, "-release", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				ioc_data.cmd = OAK_IOCTL_LGEN_RELEASE ;
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}

			if (ioc_data.cmd == 0) {
				err = IOCTL_CMD_STATUS_ERROR ;
			}

                	err = ScanArgList(argc, argv, "-c", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				ioc_data.channel = StringToDecHexInt(argv[pos]);
                	}
			else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				return (err) ;
			}

                	err = ScanArgList(argc, argv, "-n", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				ioc_data.count = StringToDecHexInt(argv[pos]);
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK;
			}

		}
	}
	return (err) ;
}

int LoadGenerator :: preAction()
{
	if (err == IOCTL_CMD_STATUS_OK && if_name != NULL) {
		if (SetDevice(if_name) == false) {
			err = IOCTL_CMD_STATUS_ERROR ;
		}
	}
	return (err) ;
}

int LoadGenerator :: Action()
{
	unsigned int l ;

	if (ioc_data.cmd > 0) {
		ioc_data.error = 0 ;

		err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;

		cout << "request=" << ioc_data.cmd << " txr=" << ioc_data.channel << " cnt=" << ioc_data.count << hex <<
			" err=" << dec << err << " completion code=" << ioc_data.error << endl ;

		if (ioc_data.error != 0) {
			err = IOCTL_CMD_STATUS_ERROR ;
		}
	}
	else {
		err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool LoadGenerator::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool LoadGenerator::isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "lgen") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
