/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef FILE_FILEDUMP_H
#define FILE_FILEDUMP_H

#include "File.h"

#define	MAX_NUMBER_OF_INPUT_FILES	16

class FileDump: public File, public Cli
{
	public:
		FileDump(int argc, char* argv[]) ;
		void	ContinueWithRead(void) ;

		virtual int		postAction(void);
		virtual int		preAction(void);
		virtual int		Action(void);
		virtual void		DumpBuffer(char *p, unsigned int len, unsigned int offs) {
						DisplayPacketAsHexDump(p,len,1,true,16,offs) ;
					}
		virtual CliArgs_t *     SetArgumentList(uint32_t *arg_list_size) ;
                virtual int		EvaluateArgumentList(int argc, char *argv[]) ;

		bool			NextInputFile() ;
		bool			IsLastInputFile() ;
		static bool		isResponsible(int argc, char* argv[]);
		static string		help(void);

	protected:
		char		*file_name[MAX_NUMBER_OF_INPUT_FILES] ;
		uint32_t	num_of_input_files ;
		uint32_t	next_input_file ;
		char		file_buf[128] ;
		size_t		file_size ;
		size_t		file_offs ;
		size_t		byte_read ;
		bool		file_end ;



};

#endif // FILE_FILEDUMP_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
