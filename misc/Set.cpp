/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "Cli.h"
#include "Set.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
	{ "-if",  1, 1 },	// Just 1 argument == interface name
	{ "-A", 1, 1 },		// Set bandwidth class A
	{ "-B", 1, 1 },		// Set bandwidth class B
	{ "-Gb",0, 0 },		// Giga bit
	{ "-Mb",0, 0 },		// Mega bit
	{ "-Kb",0, 0 },		// Kilo bit
	{ "-t", 1, 1 },		// TX ring index
	{ "-v", 0, 0 },		// be verbose
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     Set::Set(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

Set::Set(int argc, char* argv[]) : Cli(argc, argv), Socket()
{
	if_name = NULL ;

	ioc_cmd = 0 ;
	ioc_data.idx = 0 ;
	ioc_data.data = 0 ;
}

CliArgs_t * Set :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int Set :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	ioc_cmd = 0 ;
	if (err == IOCTL_CMD_STATUS_OK) {
                err = ScanArgList(argc, argv, "-if", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
			if_name = argv[pos] ;

                	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				verbose = true ;
                	}

                	err = ScanArgList(argc, argv, "-t", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				ioc_data.idx  = StringToDecHexInt(argv[pos]);
				ioc_data.idx += 1 ;	// starting ring index at 1
			}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}

			if (err == IOCTL_CMD_STATUS_OK) {
                		err = ScanArgList(argc, argv, "-A", &pos, &num) ;
                		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
					ioc_cmd = OAK_IOCTL_SET_MAC_RATE_A ;
					ioc_data.data = StringToDecHexInt(argv[pos]);
				}
				else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
                			err = ScanArgList(argc, argv, "-B", &pos, &num) ;
                			if (err == IOCTL_CMD_STATUS_OK && num == 1) {
						ioc_cmd = OAK_IOCTL_SET_MAC_RATE_B ;
						ioc_data.data = StringToDecHexInt(argv[pos]);
					}
				}

				if (err == IOCTL_CMD_STATUS_OK) {
                			err = ScanArgList(argc, argv, "-Gb", &pos, &num) ;
					if (err == IOCTL_CMD_STATUS_OK) {
						ioc_data.data *= 1000000 ;	// -> Kilo bit
					}
					else {
                				err = ScanArgList(argc, argv, "-Mb", &pos, &num) ;
						if (err == IOCTL_CMD_STATUS_OK) {
							ioc_data.data *= 1000 ;		// -> Kilo bit
						}
						else {
                					err = ScanArgList(argc, argv, "-Kb", &pos, &num) ;
						}
					}
					if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
						err = IOCTL_CMD_STATUS_OK ;
					}
				}
			}
		}
	}
	if (ioc_cmd == 0) {
		// err = IOCTL_CMD_STATUS_ERROR ;
	}
	return (err) ;
}

int Set :: preAction()
{
	if (err == IOCTL_CMD_STATUS_OK && if_name != NULL) {
		if (SetDevice(if_name) == false) {
			err = IOCTL_CMD_STATUS_ERROR ;
		}
	}
	return (err) ;
}

int Set :: Action()
{
	if (err == 0) {
		ioc_data.data /=  64 ;
		err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
	}

	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool Set::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool Set::isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "set") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
