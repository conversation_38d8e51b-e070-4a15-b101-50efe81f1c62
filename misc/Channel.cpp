/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "Cli.h"
#include "Channel.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
	{ "-if",  1, 1 },	// Just 1 argument == interface name
	{ "-rx",  1, 1 },	// Show TX channel 
	{ "-rb",  1, 1 },	// Show RX buffer of channel 
	{ "-tx",  1, 1 },	// Show RX channel
	{ "-o",	  1, 1 },	// descriptor offset
	{ "-n",	  1, 1 },	// number of descriptors 
	{ "-v",	  0, 0 },	// be verbose
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     Channel::Channel(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

Channel::Channel(int argc, char* argv[]) : Cli(argc, argv), Socket()
{
	if_name = NULL ;

	ioc_cmd = OAK_IOCTL_STAT ;
	ioc_data.cmd = 0 ;
	ioc_data.idx = 0 ;
	ioc_data.offs= 0 ;
	count = 1 ;
	cmd = 0 ;
}

CliArgs_t * Channel :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int Channel :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	if (err == IOCTL_CMD_STATUS_OK) {
                err = ScanArgList(argc, argv, "-if", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
			if_name = argv[pos] ;

                	err = ScanArgList(argc, argv, "-tx", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
				ioc_data.idx = StringToDecHexInt(argv[pos]);
				cmd = OAK_IOCTL_STAT_GET_TXC ;
                	}
			else {
                		err = ScanArgList(argc, argv, "-rx", &pos, &num) ;
                		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
					ioc_data.idx = StringToDecHexInt(argv[pos]);
					cmd = OAK_IOCTL_STAT_GET_RXC ;
                		}
				else {
                			err = ScanArgList(argc, argv, "-rb", &pos, &num) ;
                			if (err == IOCTL_CMD_STATUS_OK && num == 1) {
						ioc_data.idx = StringToDecHexInt(argv[pos]);
						cmd = OAK_IOCTL_STAT_GET_RXB ;
					}
					else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
						return (err) ;
					}
				}
			}
			if (cmd != IOCTL_CMD_STATUS_OK) {
                		err = ScanArgList(argc, argv, "-n", &pos, &num) ;
                		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
					count = StringToDecHexInt(argv[pos]);
                		}
				else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
					err = IOCTL_CMD_STATUS_OK ;
				}
			}
			if (cmd != IOCTL_CMD_STATUS_OK) {
                		err = ScanArgList(argc, argv, "-o", &pos, &num) ;
                		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
					ioc_data.offs = StringToDecHexInt(argv[pos]);
                		}
				else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
					err = IOCTL_CMD_STATUS_OK ;
				}
			}
                	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				verbose = true ;
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}
		}
	}
	return (err) ;
}

int Channel :: preAction()
{
	if (err == IOCTL_CMD_STATUS_OK && if_name != NULL) {
		if (SetDevice(if_name) == false) {
			err = IOCTL_CMD_STATUS_ERROR ;
		}
	}
	return (err) ;
}

int Channel :: Action()
{

	if (cmd > 0) {
		uint32_t num = 0 ;

		if (cmd == OAK_IOCTL_STAT_GET_RXC || cmd == OAK_IOCTL_STAT_GET_RXB) {
			ioc_data.cmd = OAK_IOCTL_STAT_GET_RXS ;
			err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
		}
		else if (cmd == OAK_IOCTL_STAT_GET_TXC) {
			ioc_data.cmd = OAK_IOCTL_STAT_GET_TXS ;
			err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
		}

		if (err == 0) {
			memcpy (&c_info, ioc_data.data, sizeof(c_info)) ;
			DisplayChannelStatus() ;
		}

		ioc_data.cmd = cmd ;

		while (err == 0 && count > 0) {

			ioc_data.error = 0 ;
			err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;

			if (verbose == true) {
				cout << "request=" << ioc_data.cmd << " idx=" << ioc_data.idx << " offs=" << ioc_data.offs << 
					" err=" << dec << err << " completion code=" << ioc_data.error << endl ;
			}

			if (ioc_data.error != 0) {
				err = IOCTL_CMD_STATUS_ERROR ;
			}
			if (err == 0) {
				if (ioc_data.cmd == OAK_IOCTL_STAT_GET_RXC) {
					DisplayRxChannel(ioc_data.offs) ;
				}
				else if (ioc_data.cmd == OAK_IOCTL_STAT_GET_TXC) {
					DisplayTxChannel(ioc_data.offs) ;
				}
				else if (ioc_data.cmd == OAK_IOCTL_STAT_GET_RXB) {
					DisplayRbChannel(ioc_data.offs) ;
				}
			}
			++ ioc_data.offs ;
			--count ;
			++num ;
		}
	}
	else {
		err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND ;
	}
	return (err) ;
}

void Channel :: DisplayChannelStatus()
{
	oak_chan_info *stat = &c_info ;

	cout << endl << "General channel information:" << endl ;

	cout << endl << "   Flags Ring-Size Pending W-Idx R-Idx R-Len" << endl ;
	cout << hex << setw(8) << stat->flags	<< " " <<
		dec << setw(9) << stat->r_size	<< " " <<
		dec << setw(7) << stat->r_pend	<< " " <<
		dec << setw(5) << stat->r_widx	<< " " <<
		dec << setw(5) << stat->r_ridx	<< " " <<
		dec << setw(5) << stat->r_len	<< " " <<
		endl;


        // uint32_t        ring_m_count ;  /* Max. elements to tx/rx (lgen) */
}

void Channel :: DisplayRxChannel(uint32_t idx)
{
	oak_rxs_t	*rxs = (oak_rxs_t *) ioc_data.data ;
	const char		*r = " " ;
	const char		*w = " " ;
	const char		*l4p = "-?-" ;
	const char		*l2p = "-?-" ;

	if (idx == 0) {
		cout << endl << "#### BCount F L ES1 ES2 I4 L4 L4P L3I4 L3I6 L2P Vlan TimeStamp  Rx-Csum  UdpCsum Pos" << endl ;
		cout         << "------------------------------------------------------------------------------------" << endl ;
	}

	if (idx == c_info.r_ridx) {
		r = "R" ;
	}
	if (idx == c_info.r_widx) {
		w = "W" ;
	}

	if (rxs->l4_prot == 1) {
		l4p = "tcp" ;
	}
	else if (rxs->l4_prot == 2) {
		l4p = "udp" ;
	}
	else if (rxs->l4_prot == 3) {
		l4p = "res" ;
	}

	if (rxs->l2_prot == 1) {
		l2p = "eth" ;
	}
	else if (rxs->l2_prot == 2) {
		l2p = "llc" ;
	}
	else if (rxs->l2_prot == 3) {
		l2p = "res" ;
	}
	
	cout << setfill('0') <<
		 dec << setw(4) << idx			<< " " <<
		 dec << setw(6) << rxs->bc		<< " " <<
		 dec << setw(2) << rxs->first_last	<< " " <<
#if 0
		 dec << setw(1) << rxs->last		<< " " <<
#endif
		 dec << setw(3) << rxs->es		<< " " <<
		 dec << setw(3) << rxs->ec		<< " " <<
		 dec << setw(2) << rxs->ipv4_hdr_ok	<< " " <<
		 dec << setw(2) << rxs->l4_chk_ok	<< " " <<
		        setw(3) << l4p			<< " " <<
		 dec << setw(4) << rxs->l3_ipv4		<< " " <<
		 dec << setw(4) << rxs->l3_ipv6		<< " " <<
		        setw(3) << l2p			<< " " <<
		 dec << setw(4) << rxs->vlan		<< " " <<
		 dec << setw(9) << rxs->timestamp	<< " " <<
		 dec << setw(8) << rxs->rc_chksum	<< " " <<
		 dec << setw(8) << rxs->udp_cs_0	<< "  " <<
		 r << w <<
		endl ;
}

void Channel :: DisplayRbChannel(uint32_t idx)
{
	oak_rxd_t	*rbr = (oak_rxd_t *) ioc_data.data ;
	const char	*r = " " ;
	const char	*w = " " ;

	if (idx == 0) {
		cout << endl << "####  Addr-Hi  Addr-Lo Pos" << endl ;
		cout         << "--------------------------" << endl ;
	}

	if (idx == c_info.r_ridx) {
		r = "R" ;
	}
	if (idx == c_info.r_widx) {
		w = "W" ;
	}

	cout << setfill('0') <<
		 dec << setw(4) << idx			<< " " <<
		 hex << setw(8) << rbr->buf_ptr_hi	<< " " <<
		 hex << setw(8) << rbr->buf_ptr_lo	<< " " <<
		 r << w <<
		endl ;
}

void Channel :: DisplayTxChannel(uint32_t idx)
{
	oak_txd_t	*txd = (oak_txd_t *) ioc_data.data ;
	const char	*r = " " ;
	const char	*w = " " ;

	if (idx == 0) {
		cout << endl << "#### BCount F L     CS-3     CS-4 Tim TimeStamp  Addr-Hi  Addr-Lo Pos" << endl ;
		cout         << "---------------------------------------------------------------------" << endl ;
	}
	if (idx == c_info.r_ridx) {
		r = "R" ;
	}
	if (idx == c_info.r_widx) {
		w = "W" ;
	}
	cout << setfill('0') <<
		 dec << setw(4) << idx			<< " " <<
		 dec << setw(6) << txd->bc		<< " " <<
		 dec << setw(1) << txd->first		<< " " <<
		 dec << setw(1) << txd->last		<< " " <<
		 dec << setw(8) << txd->gl3_chksum	<< " " <<
		 dec << setw(8) << txd->gl4_chksum	<< " " <<
		 dec << setw(3) << txd->time_valid	<< " " <<
		 dec << setw(9) << txd->timestamp 	<< " " <<
		 hex << setw(8) << txd->buf_ptr_hi	<< " " <<
		 hex << setw(8) << txd->buf_ptr_lo	<< " " <<
		 r << w <<
		endl ;

}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool Channel::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool Channel::isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "showc") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
