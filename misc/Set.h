/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef SET_H
#define SET_H

#include "Socket.h"
#include "Cli.h"
#include "oak_ioc_set.h"

class Set : public Cli, public Socket
{
	public:
					Set(int argc, char* argv[]) ;
		virtual int		preAction(void) ;
		virtual int		Action(void);

		virtual CliArgs_t *     SetArgumentList(uint32_t *arg_list_size) ;
                virtual int		EvaluateArgumentList(int argc, char *argv[]) ;

		static bool		isResponsible(int argc, char* argv[]);
	private:
		uint32_t		fac ;

	protected:
		int		ioc_cmd ;
		oak_ioc_set	ioc_data ;
		char		*if_name ;
};

#endif // SET_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
