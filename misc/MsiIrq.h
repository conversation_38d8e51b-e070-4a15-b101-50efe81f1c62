/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef MSIIRQ_H
#define MSIIRQ_H

#include "Socket.h"
#include "Cli.h"
#include "oak_ioc_stat.h"
#include "oak_unimac_desc.h"

#define TX_DMA_BIT 0			/* copied from oak_gicu.h */
#define TX_ERR_BIT 1
#define RX_DMA_BIT 2
#define RX_ERR_BIT 3
#define UNIMAC_DMA_BIT 31

#define	MAX_NUMBER_OF_INPUT_FILES	16

class MsiIrq : public Cli, public Socket
{
	public:
					MsiIrq(int argc, char* argv[]) ;
		virtual int		preAction(void) ;
		virtual int		Action(void);

		virtual CliArgs_t *     SetArgumentList(uint32_t *arg_list_size) ;
                virtual int		EvaluateArgumentList(int argc, char *argv[]) ;

		static bool		isResponsible(int argc, char* argv[]);
	private:
		uint64_t		num_chn ;
		uint64_t		num_ldg ;

		void			DisplayMsiIrqStatus(uint64_t *data) ;

	protected:
		oak_ioc_stat	ioc_data ;
		int		ioc_cmd ;
		char		*if_name ;
};

#endif // MSIIRQ_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
