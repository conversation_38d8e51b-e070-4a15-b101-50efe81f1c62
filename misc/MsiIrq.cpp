/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "Cli.h"
#include "MsiIrq.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
	{ "-if",  1, 1 },	// Just 1 argument == interface name
	{ "-v",	  0, 0 },	// be verbose
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Dump file contents
 * @details     MsiIrq::MsiIrq(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

MsiIrq::MsiIrq(int argc, char* argv[]) : Cli(argc, argv), Socket()
{
	if_name = NULL ;

	ioc_cmd = OAK_IOCTL_STAT ;
	ioc_data.cmd = 0 ;
	ioc_data.idx = 0 ;
	ioc_data.offs= 0 ;
}

CliArgs_t * MsiIrq :: SetArgumentList(uint32_t *arg_list_size)
{
	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int MsiIrq :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	if (err == IOCTL_CMD_STATUS_OK) {
                err = ScanArgList(argc, argv, "-if", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
			if_name = argv[pos] ;

                	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK) {
				verbose = true ;
                	}
			else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
				err = IOCTL_CMD_STATUS_OK ;
			}
		}
	}
	return (err) ;
}

int MsiIrq :: preAction()
{
	if (err == IOCTL_CMD_STATUS_OK && if_name != NULL) {
		if (SetDevice(if_name) == false) {
			err = IOCTL_CMD_STATUS_ERROR ;
		}
	}
	return (err) ;
}

int MsiIrq :: Action()
{
	ioc_data.cmd = OAK_IOCTL_STAT_GET_LDG ;
	err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;

	if (err == 0) {
		uint64_t chan ;
		uint64_t *data = (uint64_t *) ioc_data.data ;

		num_ldg = data[0] ;
		num_chn = data[1] ;

		if (num_ldg > 0 && num_chn > 0) {

			// cout << "NUM LDG:" << num_ldg << " NUM_CHAN:" << num_chn << endl ;
			cout << endl ;
			cout << "MSI# Vect " ;

			for (chan=0 ; chan < num_chn; chan++) {
				cout << "Channel: " << dec << setw(2) << chan << "     "  ;
			}
			cout << endl ;

			cout << "          " ;
			for (chan=0 ; chan < num_chn; chan++) {
				cout << "Tx Te Rx Re Gi  " ;
			}
			cout << endl ;

			cout << "============" ;
			for (chan=0 ; chan < num_chn; chan++) {
				cout << "===============" ;
			}
			cout << endl ;

			DisplayMsiIrqStatus(data) ;
			while (++ioc_data.idx < num_ldg && err == 0) {
				err = Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
				if (err == 0) {
					DisplayMsiIrqStatus(data) ;
				}
			}
		}
	}

	return (err) ;
}

void MsiIrq :: DisplayMsiIrqStatus(uint64_t *data)
{
	char val ;

	cout << dec << setw(4) << ioc_data.idx ;
	cout << dec << " " ;
	cout << dec << setw(4) << data[7] ;
	cout << dec << " " ;

#if 1
	for (uint64_t i=0; i<num_chn; i++) {
		// TX
		if (data[2] & ((1ULL << TX_DMA_BIT) << (i*4))) {
			val = '*' ;
		}
		else {
			val = ' ' ;
		}
		cout << dec << setw(2) << val << " " ;
		// TX-Err
		if (data[3] & ((1ULL << TX_ERR_BIT) << (i*4))) {
			val = '*' ;
		}
		else {
			val = ' ' ;
		}
		cout << dec << setw(2) << val << " " ;
		// RX
		if (data[4] & ((1ULL << RX_DMA_BIT) << (i*4))) {
			val = '*' ;
		}
		else {
			val = ' ' ;
		}
		cout << dec << setw(2) << val << " " ;
		// RX-Err
		if (data[5] & ((1ULL << RX_ERR_BIT) << (i*4))) {
			val = '*' ;
		}
		else {
			val = ' ' ;
		}
		cout << dec << setw(2) << val << " " ;
		// Gen
		if (data[6] & (1ULL << UNIMAC_DMA_BIT)) {
			val = '*' ;
		}
		else {
			val = ' ' ;
		}
		cout << dec << setw(2) << val << "  " ;
	}
	cout << endl ;
#else
	cout << "0x" << hex << setw(8) << data[2] << " " ;
	cout << "0x" << hex << setw(8) << data[3] << " " ;
	cout << "0x" << hex << setw(8) << data[4] << " " ;
	cout << "0x" << hex << setw(8) << data[5] << " " ;
	cout << "0x" << hex << setw(8) << data[6] << " " ;
	cout << endl ;
#endif
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool MsiIrq::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool MsiIrq::isResponsible(int argc, char* argv[])
{
	if (argc >= 2 && strcmp(argv[1], "irq") == 0) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
