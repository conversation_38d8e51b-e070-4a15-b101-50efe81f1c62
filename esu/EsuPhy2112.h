/*******************************************************************************
 *
 * EsuPhy2112.h
 *
 ******************************************************************************/

#ifndef EsuPhy2112_H
#define EsuPhy2112_H

#include "Socket.h"
#include "oak_ioc_reg.h"
#include <cstdint>
#include <map>
// Current Speed
#define MRVL_88Q2112_1000BASE_T1    0x0001
#define MRVL_88Q2112_100BASE_T1     0x0000

// Auto-Negotiation Controls - reg 7.0x0200
#define MRVL_88Q2112_AN_DISABLE     0x0000
#define MRVL_88Q2112_AN_RESET       0x8000
#define MRVL_88Q2112_AN_ENABLE      0x1000
#define MRVL_88Q2112_AN_RESTART     0x0200

// Auto-Negotiation Status - reg 7.0x0201
#define MRVL_88Q2112_AN_COMPLETE    0x0020

// Auto-Negotiation Flags - reg 7.0x0203
#define MRVL_88Q2112_AN_FE_ABILITY      0x0020
#define MRVL_88Q2112_AN_GE_ABILITY      0x0080
#define MRVL_88Q2112_AN_PREFER_MASTER   0x0010
// PHY Init Return flags
#define MRVL_88Q2112_INIT_DONE              0x0001
#define MRVL_88Q2112_INIT_ERROR_ARGS        0x0100
#define MRVL_88Q2112_INIT_ERROR_SMI         0x0200
#define MRVL_88Q2112_INIT_ERROR_SAVE_MODE   0x0400
#define MRVL_88Q2112_INIT_ERROR_INIT_GE     0x0800
#define MRVL_88Q2112_INIT_ERROR_FAILED      0x1000

// PHY Init (and InitAN) Flag Arguments
#define MRVL_88Q2112_INIT_MASTER        0x0001
#define MRVL_88Q2112_INIT_AN_ENABLE     0x0002
#define MRVL_88Q2112_INIT_FE            0x0004
#define MRVL_88Q2112_INIT_GE            0x0008

#define MRVL_88Q2112_PRODUCT_ID         0x0018

// 1000 BASE-T1 Operating Mode
#define MRVL_88Q2112_MODE_LEGACY    0x06B0
#define MRVL_88Q2112_MODE_DEFAULT   0x0000
#define MRVL_88Q2112_MODE_MSK       0x07F0
#define MRVL_88Q2112_MODE_ADVERTISE 0x0002
#define MRVL_88Q2112_MODE_ERROR     0xFFFF

#define MAX_PHY_NUM    8
#define MRVL_88Q2112_SINGLE_SEND_S_ENABLE	0

#ifndef TRUE
#define TRUE  1
#endif

#ifndef FALSE
#define FALSE 0
#endif

class EsuPhy2112 : public Esu
{
	public:
		EsuPhy2112(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		    postAction(void) ;
		int     	Action(void) ;
		void		SetIfName(char *ifn) { if_name = ifn ; }
		static bool	isResponsible(int argc, char* argv[]) ;

        uint16_t regRead(uint16_t phyAddr, uint16_t devAddr, uint16_t regAddr);
        void regWrite(uint16_t phyAddr, uint16_t devAddr, uint16_t regAddr, uint16_t data);
		void delay_ms(uint16_t delayTime);

		void _applyQ2112FeSetting(uint16_t phyAddr, uint16_t isAneg);
		void _applyQ2112FeSoftReset(uint16_t phyAddr);
		char getAnegEnabled ( uint16_t phyAddr );
		void _applyQ2112GeSetting(uint16_t phyAddr, uint16_t isAneg);
		uint16_t loadOperateModeSetting(uint16_t phyAddr);
		char applyOperateModeConfig(uint16_t phyAddr);
		void _applyQ2112GeSoftReset(uint16_t phyAddr);

		char initQ2112Ge(uint16_t phyAddr);
		int setupForcedSpeedDuringLinkup(uint16_t phyAddr, uint16_t targetSpeed);
		char saveOperateModeSetting(uint16_t phyAddr, uint16_t mode);
		char manualSetOperateModeSetting(uint16_t phyAddr, uint16_t mode);
		void _applyQ2112AnegSetting(uint16_t phyAddr);
		int setAneg(uint16_t phyAddr, uint16_t isAneg, uint16_t isMaster);
		char initQ2112Ge_SingleSendS(uint16_t phyAddr);



		uint16_t getRevNum(uint16_t phyAddr);
		void initQ2112Fe ( uint16_t phyAddr );
		char checkLink (uint16_t phyAddr);
		void setMasterSlave ( uint16_t phyAddr, uint16_t forceMaster );
		uint16_t getMasterSlave(uint16_t phyAddr);
		uint16_t getModelNum ( uint16_t phyAddr );
		uint16_t setSpeed(uint16_t phyAddr, uint16_t speed);
		uint16_t getSpeed ( uint16_t phyAddr );
		unsigned char getRealTimeLinkStatus ( uint16_t phyAddr );
		uint16_t getSQIReading_16Level ( uint16_t phyAddr );
		unsigned char getCQIReading ( uint16_t phyAddr, uint16_t *IL, uint16_t *RL );
		unsigned char getVCTReading ( uint16_t phyAddr, int *distanceToFault, char *cable_status );
		uint16_t phyInit ( uint16_t phyAddr, uint16_t flags);
		int cycle_phyInit(uint16_t phyAddr, uint16_t flags);


	protected:
		int		    ExecuteCmd() ;
		virtual int	WriteIO(uint32_t dev, uint32_t reg, uint32_t data) ;
		virtual int	ReadIO(uint32_t dev, uint32_t reg, uint32_t *data) ;
		int         DoInit(void) ;
		int         DoSet(void) ;
		int         DoGet(void) ;
		int         DoDiag(void) ;
		void        getPortState(uint32_t port);
	private:
		int         phy_addr ;
		int         external_port;	
		int         internal_port;
		uint32_t    master_slave;
		uint32_t    link_speed;
		char*       if_name ;
		uint32_t    sub_cmd ;

		/*
		 * 0:normal register access, 
		 * 1:read-modify-write-or, 
		 * 2:read-modify-write-and 
		 */
		int		ioc_flag ;
} ;

#endif // EsuPhy2112_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
