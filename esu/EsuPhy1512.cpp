/*******************************************************************************
 *
 * EsuPhy1512.cpp
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;


static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-init",	0, 0 },	// init mode
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       设置转发规则
 * @details     EsuPhy1512::EsuPhy1512(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to set or get ESU forward configuration.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuPhy1512::EsuPhy1512(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	ioc_cmd = 0 ;
	sub_cmd = 0 ;
	ioc_data.offs = 0x10000 ;
}


CliArgs_t * EsuPhy1512 :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

int EsuPhy1512 :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

    // Parse interface name
    err = ScanArgList(argc, argv, "-if", &pos, &num);
    if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
        return (err);
    }
    else if (num > 0) {
        if_name = argv[pos];
    }


	// Parse init command
	err = ScanArgList(argc, argv, "-init", &pos, &num);
	if (err == IOCTL_CMD_STATUS_OK) {
		sub_cmd = 1;  // init mode
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ;
		return IOCTL_CMD_STATUS_OK;
	}

end:
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuStat::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuPhy1512::postAction(void)
{ 
	return (err) ;
}


struct PhyCommand {
    uint32_t dev;
    uint32_t reg;
    uint32_t value;
};

int EsuPhy1512::DoInit(void)
{
    // PHY1512 初始化命令序列
    static const PhyCommand initSequence[] = {
        {0x1C, 0x1A, 0xE31F}, 
        {0x1C, 0x1A, 0xE520}, 
        {0x1C, 0x19, 0x0012}, 
        {0x1C, 0x18, 0xB416}, 
        {0x1C, 0x19, 0x8021}, 
        {0x1C, 0x18, 0xB414}  
    };

	// 执行命令序列
    for (const auto& cmd : initSequence) {
        err = WriteIO(cmd.dev, cmd.reg, cmd.value);
		usleep(100);
        if (err != IOCTL_CMD_STATUS_OK) {
            return err;
        }
    }

	return err ;
}


int EsuPhy1512::Action(void)
{ 
	err = IOCTL_CMD_STATUS_ERROR ;

	if (ioc_cmd == OAK_IOCTL_REG_ESU_REQ && SetDevice(if_name) == true) {
		switch(sub_cmd) {
		case 1: err = DoInit() ;break ;
		default: err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND ;
		}
	}
	return (err) ;
}



/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuPhy1512::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuPhy1512::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 2) {
		if (strcmp(argv[1], "PHY1512") == 0 || strcmp(argv[1], "phy1512") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
