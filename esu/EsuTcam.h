/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef ESUTCAM_IO_H
#define ESUTCAM_IO_H

#include "Socket.h"
#include "oak_ioc_reg.h"

typedef struct TCAM_Field_s {
	uint32_t	b_pos_e ;	// End bit position
	uint32_t	b_pos_s ;	// Start bit position
	const char  *	b_descr ;	// Text description
	uint32_t	data ;		// Bit field data
} TCAM_Field_t ;

typedef struct TCAM_Action_s {
	uint32_t	TcamPage ;	// TCAM page order
	uint32_t	TcamBlck ;	// TCAM block id
	uint32_t	TcamOffs;	// TCAM offset
	uint32_t	FieldCnt ;	// Number of bit fields below
	TCAM_Field_t *	TCAMFlds ;	// Bit fields
	uint32_t	data ;		// Data of all bit in TCAMFlds
} TCAM_Register_t ;

typedef struct TCAM_All_Actions_s {
	uint32_t	Elements ;	// Number of elements in Action below
	TCAM_Register_t *	Action ;
} TCAM_All_Actions_t ;

// class EsuTcam : public Cli, public Socket
class EsuTcam : public Esu, public File
{
	public:
				EsuTcam(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;
		void		SetIfName(char *ifn) { if_name = ifn ; }
		

	static	bool		isResponsible(int argc, char* argv[]) ;

	protected:
		void		SetActionParameterBits(TCAM_Field_t *a, uint32_t val) ;
		bool		SetValidActionParameter(char *act) ;
		bool		SetActionParameter(string s, uint32_t val) ;
		TCAM_Field_t *	TcamGetRegisterFieldPointer(string s) ;
		TCAM_Register_t *
				TcamGetRegisterPointer(uint32_t page, uint32_t block, uint32_t offs) ;

		int		ReadActionArgumentList(char *argv[], int pos, int num) ;
#if 0
		int		WriteIO(uint32_t dev, uint32_t reg, uint32_t data) ;
		int		ReadIO(uint32_t dev, uint32_t reg, uint32_t *data) ;
#endif
		int 		CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val) ;
		int 		PrintRegisterList(bool print_data = false)  ;
		int 		DoFlush(void)  ;
		int 		DoList(void)  ;
		int 		DoRead(void)  ;
		int 		DoAdd(void)  ;
		int 		ExecuteCmd(uint32_t cmd, uint32_t page);
		int 		GetNextEntry(uint32_t opcode) ;
		int 		DisplayTcamEntry() ;

		int		TcamCmdDone(uint32_t reg, uint32_t b, uint32_t val) ;
		int		TcamReadIO(uint32_t reg, uint32_t *data) ;
		int		TcamWriteIO(uint32_t reg, uint32_t data) ;

		void		SetPortMask() ;
		int		GetPortMask() ;
		int		TcamPutPageActions(TCAM_Register_t *action) ;
		int		TcamPutListToPage(uint32_t page) ;
		int		TcamGetPageActions(TCAM_Register_t *action) ;
		int		TcamGetPageToList(uint32_t page) ;
		int		TcamGetPage_0() ;
		int		TcamWritePage_0(uint32_t opcode) ;
		int		TcamWritePage_1(uint32_t opcode) ;
		int		TcamWritePage_2(uint32_t opcode) ;
		int		TcamWritePage_3(uint32_t opcode) ;
		int		TcamReadPage_0(uint32_t opcode) ;
		int		TcamReadPage_1(uint32_t opcode) ;
		int		TcamReadPage_2(uint32_t opcode) ;
		int		TcamReadPage_3(uint32_t opcode) ;

		int		EsuSetPortTcam(uint32_t port, uint32_t mode) ;
		int		EsuEnablePortForTcam(uint32_t port_mask) ;
		int		EsuDisablePortForTcam(uint32_t port_mask) ;

	private:
		char	*	GetDataOffset(char *ptr, char **end, int *offs, char *tag) ;
		char 	*	GetDataBytes(char *ptr, char *end, int offs, bool mask=false) ;
		int		ExecuteLine(char *ptr, int len, char *tag) ;
		int		ReadDataFile(void) ;

		char	*	if_name ;
		// int		ioc_cmd ;
		uint32_t	sub_cmd ;

		uint32_t	SPV ;		// 12 bits / 12 ports
		uint32_t	FrameType ;	// 0 := normal frame

		uint32_t	TCAM_Entry ;
		uint32_t	TCAM_Mode ;
		char	*	Data_File ;

		char		file_buf[128] ;
		char		frame[48] ;
		char		fmask[48] ;
		int		r_offs ;

		uint32_t	verbose_flags ;

		/* 0:normal register access, 1:read-modify-write-or, 2:read-modify-write-and */
		// oak_ioc_reg	ioc_data ;
} ;

#endif // ESUTCAM_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
