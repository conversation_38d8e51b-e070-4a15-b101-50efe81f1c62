/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

typedef struct esu_stat_s {
	const char *	descr ;	// Register description
	uint32_t	bank ;	// bank index
	uint32_t	offs ;	// Register offset
	uint32_t	num ;	// number of 32 bit words to read
} esu_stat_t ;

static esu_stat_t stat_counters[] = {
	{ "InGoodOctets",	0, 0x00, 2, },
	{ "InBadOctets ",	0, 0x02, 1, },
	{ "InUnicast"	,	0, 0x04, 1, },
	{ "InBroadcasts",	0, 0x06, 1, },
	{ "InMulticasts",	0, 0x07, 1, },
	{ "InPause"	,	0, 0x16, 1, },
	{ "InUndersize",	0, 0x18, 1, },
	{ "InFragments",	0, 0x19, 1, },
	{ "InOversize",		0, 0x1A, 1, },
	{ "InJabber",		0, 0x1B, 1, },
	{ "InRxErr",		0, 0x1C, 1, },
	{ "InFCSErr",		0, 0x1D, 1, },

	{ "OutOctets",		0, 0x0E, 2, },
	{ "OutUnicasts",	0, 0x10, 1, },
	{ "OutBroadcasts",	0, 0x13, 1, },
	{ "OutMulticasts",	0, 0x12, 1, },
	{ "OutPause",		0, 0x15, 1, },
	{ "Collisions",		0, 0x1E, 1, },
	{ "Deferred",		0, 0x05, 1, },
	{ "Single",		0, 0x14, 1, },
	{ "Multiple",		0, 0x17, 1, },
	{ "OutFCSErr",		0, 0x03, 1, },
	{ "Excessive",		0, 0x11, 1, },
	{ "Late",		0, 0x1F, 1, },

	{ "Octets   64",	0, 0x08, 1, },
	{ "Octets  127",	0, 0x09, 1, },
	{ "Octets  255",	0, 0x0A, 1, },
	{ "Octets  511",	0, 0x0B, 1, },
	{ "Octets 1023",	0, 0x0C, 1, },
	{ "Octets 1024",	0, 0x0D, 1, },

	{ "InDiscards",		1, 0x00, 1, },
	{ "InDiscardsYel",	1, 0x1D, 1, },
	{ "InFiltered",		1, 0x01, 1, },
	{ "InAccepted",		1, 0x02, 1, },
	{ "InBadAccepted",	1, 0x03, 1, },
	{ "InGoodAVBClassA",	1, 0x04, 1, },
	{ "InGoodAVBClassB",	1, 0x05, 1, },
	{ "InBadAVBClassA",	1, 0x06, 1, },
	{ "InBadAVBClassB",	1, 0x07, 1, },
	{ "InBadQbv",		1, 0x19, 1, },

	{ "TCAMCounter0",	1, 0x08, 1, },
	{ "TCAMCounter1",	1, 0x09, 1, },
	{ "TCAMCounter2",	1, 0x0A, 1, },
	{ "TCAMCounter3",	1, 0x0B, 1, },

	{ "InDroppedAvbA",	1, 0x0C, 1, },
	{ "InDroppedAvbB",	1, 0x0D, 1, },
	{ "InDaUnknown"	,	1, 0x0E, 1, },
	{ "InMGMT",		1, 0x0F, 1, },

	{ "OutQueue0",		1, 0x10, 1, },
	{ "OutQueue1",		1, 0x11, 1, },
	{ "OutQueue2",		1, 0x12, 1, },
	{ "OutQueue3",		1, 0x13, 1, },
	{ "OutQueue4",		1, 0x14, 1, },
	{ "OutQueue5",		1, 0x15, 1, },
	{ "OutQueue6",		1, 0x16, 1, },
	{ "OutQueue7",		1, 0x17, 1, },

	{ "OutCutThrough",	1, 0x18, 1, },
	{ "OutOctetsA",		1, 0x1A, 1, },
	{ "OutOctetsB",		1, 0x1B, 1, },
	{ "OutYellow",		1, 0x1C, 1, },
	{ "OutDiscards",	1, 0x1E, 1, },
	{ "OutMgmt",		1, 0x1F, 1, },
} ;

static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-show",	0, 1 },	// Read ATU entries
        { "-flush",	0, 1 },	// Flush all (FID) entries
        { "-v",		0, 0 },	// verbose option
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     EsuStat::EsuStat(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuStat::EsuStat(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	ioc_cmd = 0 ;
	sub_cmd = 5 ;	// Capture all couunters for a port
	port = -1 ;
	ioc_data.offs = 0x10000 ;
}

CliArgs_t * EsuStat :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuStat :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-flush", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			port = StringToDecHexInt(argv[pos]) ;
			sub_cmd = 2 ;	// flush one port
		}
		else {
			sub_cmd = 1 ;	// flush all ports
		}
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		goto end ;
	}
	
	err = ScanArgList(argc, argv, "-show", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			port = StringToDecHexInt(argv[pos]) ;
		}
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
	}

end:
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuStat::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuStat::postAction(void)
{ 
}

int EsuStat::ExecuteCmd(uint32_t cmd, uint32_t bank, uint32_t port, uint32_t ptr)
{
	uint32_t command = (1 << 15) | ((cmd & 7) << 12) | (bank << 10) | ((port+1) << 5) | (ptr & 0x1F) ;

	err = WriteIO(0x1B, 0x1D, command) ;
	if (err == 0) {
		err = CmdDone(0x1B, 0x1D, 15, 0) ;
	}
	return (err) ;
}

int EsuStat::CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val)
{
	uint32_t	data ;
	
	val &= 1 ;
	err = 0 ;
	for (int i=0; i<100 && err==0 ; i++) {
		err = ReadIO(dev, reg, &data) ;
		if (err == 0 && ((data >> b) & 1) == val) {
			return(0) ;
		}
		usleep(100) ;
	}
	if (err == 0) {
		err = IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}
	return (err) ;
}

int EsuStat::Action(void)
{ 
	err = IOCTL_CMD_STATUS_ERROR ;

	if (ioc_cmd == OAK_IOCTL_REG_ESU_REQ && SetDevice(if_name) == true) {
		switch(sub_cmd) {
		case 1: port = 0 ;
			// FALLTHROUGH
		case 2:	err = DoFlush();break ;
		case 5:	err = DoStat() ;break ;
		}
	}
	return (err) ;
}

int EsuStat::DoPortBankStat(uint32_t port, uint32_t bank) 
{
	err = ExecuteCmd(5, bank, port, 0) ;
}

int EsuStat::DoPortStat(uint32_t port) 
{
	esu_stat_t *ptr = stat_counters ;


	err = ExecuteCmd(5, 0, port, 0) ;

	if (err == 0) {
		cout << endl << "Port " << port << " statistic counter" << endl ;

		for (uint32_t i=0; (err == 0) && (i<sizeof(stat_counters)/sizeof(esu_stat_t)); i++) {
			uint64_t val ;

			val = 0 ;
			for (uint32_t n=0; n < ptr->num; n++) {
				err = ExecuteCmd(4, ptr->bank, port, ptr->offs+n) ;

				if (err == 0) {
					uint32_t v0 ;
					uint32_t v1 ;

					err = ReadIO(0x1B, 0x1E, &v1) ;	if (err != 0) goto end ;
					err = ReadIO(0x1B, 0x1F, &v0) ;	if (err != 0) goto end ;
					v0 &= 0xFFFF ;
					v1 &= 0xFFFF ;
					val += ((v1 << 16) | v0) ;

					if ((n+1) >= ptr->num) {
						if (((i%2)==0 && (i > 0))) {
							cout << endl ;
						}
						cout << setw(24) << left <<  ptr->descr << " : " << setw(16) << right << dec << val << "        " ;
					}
				}
				else {
					break ;
				}
			}
			++ptr ;
		}
	}
end:
	return(err) ;
}

int EsuStat::DoStat(void) 
{
	if (port == -1) {
		err = 0 ;
		for (port=0; port <= 11 && err == 0; port++) {
			err = DoPortStat(port) ;
		}
	}
	else {
		err = DoPortStat(port) ;
	}
	
	return (err) ;
}

int EsuStat::DoFlush(void) 
{
	err = ExecuteCmd(sub_cmd, 0, port, 0) ;
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuStat::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuStat::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 2) {
		if (strcmp(argv[1], "ESTAT") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
