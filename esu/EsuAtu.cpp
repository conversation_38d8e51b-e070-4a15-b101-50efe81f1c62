/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-list",	0, 1 },	// Read ATU entries
        { "-flush",	0, 1 },	// Flush all (FID) entries
        { "-clear",	0, 1 },	// Flush  non static (FID) entries
        { "-add",	1, 6 },	// Add  entries
        { "-scan",	0, 0 },	// Scan all addresses for all FIDs
        { "-v",		0, 0 },	// verbose option
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     EsuAtu::EsuAtu(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuAtu::EsuAtu(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	ioc_cmd = 0 ;
	sub_cmd = 0 ;	// list
	fid = 0 ;	// FID == 0 (common)
	qpri = 0 ;	// FID == 0 (common)
	fpri = 0 ;	// FID == 0 (common)
	entry  = 0xF ;	// static entry
	vector = 0xfff ;// all ports
	ioc_data.offs = 0x10000 ;
}

CliArgs_t * EsuAtu :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuAtu :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 4 ;	// get next
		if (num == 1) {
			fid = StringToDecHexInt(argv[pos]) ;
		}
		goto end ;
	}
	
	err = ScanArgList(argc, argv, "-flush", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		if (num == 1) {
			fid = StringToDecHexInt(argv[pos]) ;
			sub_cmd = 5 ;	// Flush all entries of FID
		}
		else {
			sub_cmd = 1 ;	// Flush all entries
		}
		goto end ;
	}
	
	err = ScanArgList(argc, argv, "-clear", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		if (num == 1) {
			fid = StringToDecHexInt(argv[pos]) ;
			sub_cmd = 6 ;	// Flush all non-static entries of FID
		}
		else {
			sub_cmd = 2 ;	// Flush all non-static entries
		}
		goto end ;
	}

	err = ScanArgList(argc, argv, "-scan", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 0 ;	
		goto end ;
	}

	err = ScanArgList(argc, argv, "-add", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num > 0) {
		uint64_t u64 ;
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 3 ;	// Flush all non-static entries of FID

		u64 = StringToMACAddress(argv[pos], maddr) ;
		// cout << "ADDR:" << hex << u64 << endl ;

		if (u64 == 0) {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
		}
		else {
			if (num >= 2) {
				entry = StringToDecHexInt(argv[pos+1]) ;
			}

			if (num >= 3) {
				vector = StringToDecHexInt(argv[pos+2]) ;
			}

			if (num >= 4) {
				fid = StringToDecHexInt(argv[pos+3]) ;
			}

			if (num >= 5) {
				qpri = StringToDecHexInt(argv[pos+4]) ;
			}

			if (num == 6) {
				fpri = StringToDecHexInt(argv[pos+5]) ;
			}
		}
	}
end:
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuAtu::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuAtu::postAction(void)
{ 
}

#if 0
int EsuAtu::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_WR ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	ioc_data.data = data ;
	return (Ioctl(ioc_cmd, (caddr_t) &ioc_data)) ;
}

int EsuAtu::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_RD ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	err =  Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
	*data = ioc_data.data ;
	// cout << "Read: " << err << " data=" << hex << setw(4) << *data << endl ;
	return (err) ;
}
#endif

int EsuAtu::ExecuteCmd(uint32_t cmd, uint32_t qpri, uint32_t fpri)
{
	uint32_t command = (1 << 15) | ((cmd & 7) << 12) | (qpri << 8) | (fpri << 0) ;

	err = WriteIO(0x1B, 0x0B, command) ;
	if (err == 0) {
		err = CmdDone(0x1B, 0x0B, 15, 0) ;
	}
	return (err) ;
}

int EsuAtu::CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val)
{
	uint32_t	data ;
	
	val &= 1 ;
	err = 0 ;
	for (int i=0; i<100 && err==0 ; i++) {
		err = ReadIO(dev, reg, &data) ;
		if (err == 0 && ((data >> b) & 1) == val) {
			return(0) ;
		}
		usleep(100) ;
	}
	if (err == 0) {
		err = IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}
	return (err) ;
}

int EsuAtu::Action(void)
{ 
	err = IOCTL_CMD_STATUS_ERROR ;

	if (ioc_cmd == OAK_IOCTL_REG_ESU_REQ && SetDevice(if_name) == true) {
		switch(sub_cmd) {
		case 1:	// FALLTHROUGH
		case 2:	// FALLTHROUGH
		case 5:	// FALLTHROUGH
		case 6:	err = DoClear() ;break ;
		case 3: err = DoAdd() ;  break ;
		case 4: err = DoList() ; break ;
		case 0: err = DoScan() ; break ;
		}
	}
	return (err) ;
}

int EsuAtu::DoAdd(void) 
{
	uint32_t data ;
	
	// cout << "FID: " << hex << fid << endl ;
	err = WriteIO(0x1B, 0x1, (fid & 0xFFF)) ; if (err != 0) return(err) ;

	// Write ATU start address
	data = maddr[5] & 0xFF ;
	data = (data << 8) | (maddr[4] & 0xFF) ;
	err = WriteIO(0x1B, 0xD, data) ; if (err != 0) return(err) ;

	data = maddr[3] & 0xFF ;
	data = (data << 8) | (maddr[2] & 0xFF) ;
	err = WriteIO(0x1B, 0xE, data) ; if (err != 0) return(err) ;

	data = maddr[1] & 0xFF ;
	data = (data << 8) | (maddr[0] & 0xFF) ;
	err = WriteIO(0x1B, 0xF, data) ; if (err != 0) return(err) ;
	
	data = ((vector & 0x7FF) << 4) | (entry & 0xF) ;
	if (vector & (1 << 11)) {
		data |= (1 << 16) ;
	}
	err = WriteIO(0x1B, 0xC, data) ;
	if (err == 0) {
		err = ExecuteCmd(sub_cmd, qpri, fpri) ;
	}

	return (err) ;
}

int EsuAtu::DoClear(void) 
{
	err = WriteIO(0x1B, 0x01, (fid & 0xFFF)) ;
	if (err == 0) {
		err = ExecuteCmd(sub_cmd) ;
	}
	return (err) ;
}

int EsuAtu::DoScan(void) 
{
	uint32_t	cnt, tot, db ;
	int		rc ;

	sub_cmd = 4 ;

	cout << endl ;
	cout << "Entry  MAC Address ES             Port - Vector             Overwrite  FID" << endl ;
	cout << "                       11 10 09 08 07 06 05 04 03 02 01 00  QPri FPri     " << endl ;
	cout << "--------------------------------------------------------------------------" << endl ;

	err = 0 ;
	tot = 0 ;
	for (db=0; db < 0xFFF && err == 0; db++) {
		err = WriteIO(0x1B, 0x01, (db & 0xFFF)) ; if (err != 0) return(err) ;

		// Write ATU start address
		err = WriteIO(0x1B, 0x0D, 0xFFFF) ; if (err != 0) return(err) ;
		err = WriteIO(0x1B, 0x0E, 0xFFFF) ; if (err != 0) return(err) ;
		err = WriteIO(0x1B, 0x0F, 0xFFFF) ; if (err != 0) return(err) ;

		cnt = 0 ;
		rc  = 1 ;
		while (cnt <= 0xFFF && rc == 1) {
			rc = GetNextEntry(tot) ;
			// cout << "DB:" << dec << db << " cnt:" << cnt << " rc:" << rc << endl ;
			++cnt ;
			tot += rc ;
		}
	}
	cout << "done" << endl ;
	return err ;
}

int EsuAtu::GetNextEntry(uint32_t idx) 
{
	uint32_t	w0, w1, w2 ;
	uint32_t	r_fid, r_dat, r_opc ;
	string s ;

	// ATU read command
	err = ExecuteCmd(sub_cmd) ;	if (err != 0) return(-1) ;

	err = ReadIO(0x1B, 0x0D, &w0) ; if (err != 0) return(-1) ;
	err = ReadIO(0x1B, 0x0E, &w1) ; if (err != 0) return(-1) ;
	err = ReadIO(0x1B, 0x0F, &w2) ; if (err != 0) return(-1) ;

	if (w0 == 0xffff && w1 == 0xffff && w2 == 0xffff) {
		return(0) ;
	}

	err = ReadIO(0x1B, 0x01, &r_fid) ; if (err != 0) return(-1) ;
	// err = ReadIO(0x1B, 0x09, &fpd) ; if (err != 0) return(-1) ;
	err = ReadIO(0x1B, 0x0C, &r_dat) ; if (err != 0) return(-1) ;
	err = ReadIO(0x1B, 0x0B, &r_opc) ; if (err != 0) return(-1) ;

	s =  ((r_dat & (1 << 16)) != 0) ? " * " : "   " ;
	for (uint32_t b=14; b>=4; b--) {
		s +=  ((r_dat & (1 << b)) != 0) ? " * " : "   " ;
	}

	cout << dec << setw(5) << setfill(' ') << idx << " " ;
	cout << hex << setw(4) << setfill('0') << w0 ;
	cout << hex << setw(4) << setfill('0') << w1 ;
	cout << hex << setw(4) << setfill('0') << w2 << " " ;
	cout << hex << setw(2) << setfill(' ') << (r_dat & 0xF) << "  " ;
	cout << s ;
	cout << hex << setw(4) << setfill(' ') << ((r_opc >> 8) & 7)  << " " ;
	cout << hex << setw(4) << setfill(' ') << ((r_opc >> 0) & 7)  << "   "  ;
	cout << hex << setw(3) << setfill(' ') << (r_fid & 0xfff)  ;
	cout << endl ;
	return (1) ;
}

int EsuAtu::DoList(void) 
{
	uint32_t	cnt ;
	int		rc ;

	cnt = 0 ;

	err = WriteIO(0x1B, 0x01, (fid & 0xFFF)) ; if (err != 0) return(err) ;

	// Write ATU start address
	err = WriteIO(0x1B, 0x0D, 0xFFFF) ; if (err != 0) return(err) ;
	err = WriteIO(0x1B, 0x0E, 0xFFFF) ; if (err != 0) return(err) ;
	err = WriteIO(0x1B, 0x0F, 0xFFFF) ; if (err != 0) return(err) ;

	cout << endl ;
	cout << "Entry  MAC Address ES             Port - Vector             Overwrite  FID" << endl ;
	cout << "                       11 10 09 08 07 06 05 04 03 02 01 00  QPri FPri     " << endl ;
	cout << "--------------------------------------------------------------------------" << endl ;

	rc = 1 ;
	while (cnt < 512 && rc == 1) {
		rc = GetNextEntry(cnt) ;
		++cnt ;
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuAtu::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuAtu::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 2) {
		if (strcmp(argv[1], "ATU") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
