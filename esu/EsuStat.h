/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef ESUSTAT_IO_H
#define ESUSTAT_IO_H

#include "Socket.h"
#include "oak_ioc_reg.h"

// class EsuStat : public Cli, public Socket
class EsuStat : public Esu
{
	public:
				EsuStat(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;
		void		SetIfName(char *ifn) { if_name = ifn ; }
		

	static	bool		isResponsible(int argc, char* argv[]) ;

	protected:
		int 		CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val) ;
		int 		DoPortStat(uint32_t port) ;;
		int 		DoPortBankStat(uint32_t port, uint32_t bank)  ;
		int 		DoStat(void)  ;
		int 		DoFlush(void)  ;
		int		ExecuteCmd(uint32_t cmd, uint32_t bank, uint32_t port, uint32_t ptr) ;
	private:
		int		port ;
		char	*	if_name ;
		uint32_t	sub_cmd ;

		/* 0:normal register access, 1:read-modify-write-or, 2:read-modify-write-and */
		int		ioc_flag ;
} ;

#endif // ESUSTAT_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
