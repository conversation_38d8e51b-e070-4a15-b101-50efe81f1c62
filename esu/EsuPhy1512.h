/*******************************************************************************
 *
 * EsuForward.h
 *
 ******************************************************************************/

#ifndef ESUPHY1512_H
#define ESUPHY1512_H

#include "Socket.h"
#include "oak_ioc_reg.h"

class EsuPhy1512 : public Esu
{
	public:
		EsuPhy1512(int argc, char* argv[]) ;
		CliArgs_t *	    SetArgumentList(uint32_t *arg_list_size) ;
		int 		    EvaluateArgumentList(int argc, char *argv[]) ;
		int		        postAction(void) ;
		int	        	Action(void) ;
		void	    	SetIfName(char *ifn) { if_name = ifn ; }
		static bool     isResponsible(int argc, char* argv[]) ;


	protected:
		int             DoInit(void) ;
	private:

		char*           if_name ;
		uint32_t        sub_cmd ;

		/*
		 * 0:normal register access, 
		 * 1:read-modify-write-or, 
		 * 2:read-modify-write-and 
		 */
		int		ioc_flag ;
} ;



#endif // ESUPHY1512_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
