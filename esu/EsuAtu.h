/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef ESUATU_IO_H
#define ESUATU_IO_H

#include "Socket.h"
#include "oak_ioc_reg.h"

// class EsuAtu : public Cli, public Socket
class EsuAtu : public Esu
{
	public:
				EsuAtu(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;
		void		SetIfName(char *ifn) { if_name = ifn ; }
		

	static	bool		isResponsible(int argc, char* argv[]) ;

	protected:
#if 0
		int		WriteIO(uint32_t dev, uint32_t reg, uint32_t data) ;
		int		ReadIO(uint32_t dev, uint32_t reg, uint32_t *data) ;
#endif
		int 		CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val) ;
		int 		DoClear(void)  ;
		int 		DoList(void)  ;
		int 		DoScan(void)  ;
		int 		DoAdd(void)  ;
		int 		ExecuteCmd(uint32_t cmd, uint32_t qpri=0, uint32_t fpri=0);
		int 		GetNextEntry(uint32_t idx) ;
	private:
		char	*	if_name ;
		// int		ioc_cmd ;
		uint32_t	sub_cmd ;
		uint32_t	fid ;
		uint32_t	qpri ;
		uint32_t	fpri ;
		uint32_t	entry ;
		uint32_t	vector ;
		unsigned char	maddr[8] ;

		/* 0:normal register access, 1:read-modify-write-or, 2:read-modify-write-and */
		int		ioc_flag ;
		// oak_ioc_reg	ioc_data ;
} ;

#endif // ESUATU_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
