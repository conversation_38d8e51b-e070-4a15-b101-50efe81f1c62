/*******************************************************************************
 *
 * EsuPhy2112.cpp
 *
 ******************************************************************************/

#include "../CliFactory.h"
#include <unistd.h>
#include <cstdint>

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-init",  0, 1 }, // Initialize PHY
        { "-set",	3, 3 },	// Set mode
        { "-get",	0, 1 },	// Get mode
        { "-diag",  0, 1 }, // Get diagnostic info (SQI, CQI, VCT)
} ;

uint16_t operate_mode[MAX_PHY_NUM] = 
{
    MRVL_88Q2112_MODE_ERROR,
    MRVL_88Q2112_MODE_ERROR,
    MRVL_88Q2112_MODE_ERROR,
    MRVL_88Q2112_MODE_ERROR,
    MRVL_88Q2112_MODE_ERROR,
    MRVL_88Q2112_MODE_ERROR,
    MRVL_88Q2112_MODE_ERROR,
    MRVL_88Q2112_MODE_ERROR
};


/* *********************************************************************************//**
 * @defgroup CMDT_PHY  CMDT PHY I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_PHY
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PHY I/O
 * @details     EsuPhy2112::EsuPhy2112(int argc, char* argv[])
 * @details     Class contructor derived from class Esu matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuPhy2112::EsuPhy2112(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	ioc_cmd = 0 ;
	sub_cmd = 0 ;
	phy_addr = -1 ;
	master_slave = 1;
	link_speed = 1;
	external_port = -1;
	internal_port = -1;
	ioc_data.offs = 0x10000 ;
}



CliArgs_t * EsuPhy2112 :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuPhy2112 :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}
	
	// Parse set command
    err = ScanArgList(argc, argv, "-set", &pos, &num);
    if (err == IOCTL_CMD_STATUS_OK) {
        sub_cmd = 1;  // set mode
        
        // Get port number if provided
        external_port = StringToDecHexInt(argv[pos]);
        phy_addr = mapExternalToPhyAddr(external_port);
        internal_port = mapExternalToInternal(external_port);
		pos++;

        
        // Parse MasterSlave and LinkSpeed parameters
        for(int i = pos; i < argc; i++) {
            if(strncmp(argv[i], "MasterSlave=", 12) == 0) {
                master_slave = StringToDecHexInt(argv[i] + 12);
            }
            else if(strncmp(argv[i], "LinkSpeed=", 10) == 0) {
                link_speed = StringToDecHexInt(argv[i] + 10);
            }
        }
        
        ioc_cmd = OAK_IOCTL_REG_ESU_REQ;
        return IOCTL_CMD_STATUS_OK;
    }
    
    // Parse get command
    err = ScanArgList(argc, argv, "-get", &pos, &num);
    if (err == IOCTL_CMD_STATUS_OK) {
        sub_cmd = 0;  // get mode
        if (num == 1) {
            external_port = StringToDecHexInt(argv[pos]);
            phy_addr = mapExternalToPhyAddr(external_port);
            internal_port = mapExternalToInternal(external_port);
        }
        ioc_cmd = OAK_IOCTL_REG_ESU_REQ;
        return IOCTL_CMD_STATUS_OK;
    }

    // Parse diag command
    err = ScanArgList(argc, argv, "-diag", &pos, &num);
    if (err == IOCTL_CMD_STATUS_OK) {
        sub_cmd = 3;  // get diagnostic info
        if (num == 1) {
            external_port = StringToDecHexInt(argv[pos]);
            phy_addr = mapExternalToPhyAddr(external_port);
            internal_port = mapExternalToInternal(external_port);
        }
        ioc_cmd = OAK_IOCTL_REG_ESU_REQ;
        return IOCTL_CMD_STATUS_OK;
    }

    // Parse init command
    err = ScanArgList(argc, argv, "-init", &pos, &num);
    if (err == IOCTL_CMD_STATUS_OK) {
        sub_cmd = 2;  // init mode
        if (num == 1) {
            external_port = StringToDecHexInt(argv[pos]);
            phy_addr = mapExternalToPhyAddr(external_port);
            internal_port = mapExternalToInternal(external_port);
        }
        ioc_cmd = OAK_IOCTL_REG_ESU_REQ;
        return IOCTL_CMD_STATUS_OK;
    }

end:
	return (err) ;
}
void EsuPhy2112::_applyQ2112FeSetting(uint16_t phyAddr, uint16_t isAneg) {
    uint16_t regData = 0;

    delay_ms(1);
    if (0x0 != isAneg)
        regWrite(phyAddr, 7, 0x0200, MRVL_88Q2112_AN_ENABLE | MRVL_88Q2112_AN_RESTART);
    else
        regWrite(phyAddr, 7, 0x0200, MRVL_88Q2112_AN_DISABLE);

        regWrite(phyAddr, 3, 0xFA07, 0x0202);
        regData = regRead(phyAddr, 1, 0x0834);
        regData = regData & 0xFFF0;
        regWrite(phyAddr, 1, 0x0834, regData);
        delay_ms(5);

        regWrite(phyAddr, 3, 0x8000, 0x0000);
        regWrite(phyAddr, 3, 0x8100, 0x0200);
        regWrite(phyAddr, 3, 0xFA1E, 0x0002);
        regWrite(phyAddr, 3, 0xFE5C, 0x2402);
        regWrite(phyAddr, 3, 0xFA12, 0x001F);
        regWrite(phyAddr, 3, 0xFA0C, 0x9E05);
        regWrite(phyAddr, 3, 0xFBDD, 0x6862);
        regWrite(phyAddr, 3, 0xFBDE, 0x736E);
        regWrite(phyAddr, 3, 0xFBDF, 0x7F79);
        regWrite(phyAddr, 3, 0xFBE0, 0x8A85);
        regWrite(phyAddr, 3, 0xFBE1, 0x9790);
        regWrite(phyAddr, 3, 0xFBE3, 0xA39D);
        regWrite(phyAddr, 3, 0xFBE4, 0xB0AA);
        regWrite(phyAddr, 3, 0xFBE5, 0x00B8);
        regWrite(phyAddr, 3, 0xFBFD, 0x0D0A);
        regWrite(phyAddr, 3, 0xFBFE, 0x0906);
        regWrite(phyAddr, 3, 0x801D, 0x8000);
        regWrite(phyAddr, 3, 0x8016, 0x0011);
}

void EsuPhy2112::_applyQ2112FeSoftReset(uint16_t phyAddr) {
    uint16_t regData = 0;
        regWrite(phyAddr, 3, 0x0900, 0x8000);
        regWrite(phyAddr, 3, 0xFA07, 0x0200);
}

void EsuPhy2112::initQ2112Fe ( uint16_t phyAddr ) {
    _applyQ2112FeSetting(phyAddr, MRVL_88Q2112_AN_DISABLE);
	regWrite(phyAddr, 31, 0x8001, 0xC000);
	regWrite(phyAddr, 3, 0x8000, 0x8000);
	delay_ms(5);
	regWrite(phyAddr, 3, 0x8000,  0x0);
    _applyQ2112FeSoftReset(phyAddr);
}


char EsuPhy2112::getAnegEnabled ( uint16_t phyAddr ) {
    return ( 0x0 != (regRead(phyAddr, 7, 0x0200) & MRVL_88Q2112_AN_ENABLE) );
}

uint16_t EsuPhy2112::getSpeed ( uint16_t phyAddr ) {
    if (getAnegEnabled(phyAddr))
        return ( (regRead(phyAddr, 7, 0x801a) & 0x4000) >> 14 );
    else
        return (regRead(phyAddr, 1, 0x0834) & 0x0001);
}

char EsuPhy2112::checkLink ( uint16_t phyAddr ) {
    uint16_t retData1, retData2, retData3;

	if (MRVL_88Q2112_1000BASE_T1 == getSpeed(phyAddr)) {
        retData1 = regRead(phyAddr, 3, 0x0901);
        retData1 = regRead(phyAddr, 3, 0x0901);	// Read twice: link latches low status
        retData2 = regRead(phyAddr, 7, 0x8001);	// local and remote receiver status
		retData3 = regRead(phyAddr, 3, 0xFD9D);	// local receiver status 2
		return (0x0004 == (retData1 & 0x0004)) && (0x3000 == (retData2 & 0x3000)) && (0x0010 == (retData3 & 0x0010));
    }
    else {
        retData1 = regRead(phyAddr, 3, 0x8109);	// link
        retData2 = regRead(phyAddr, 3, 0x8108);	// local and remote receiver status
		retData3 = regRead(phyAddr, 3, 0x8230);	// descrambler lock status
		return (0x0004 == (retData1 & 0x0004)) && (0x3000 == (retData2 & 0x3000)) && (0x1 == (retData3 & 0x1));
    }
}


void EsuPhy2112::setMasterSlave ( uint16_t phyAddr, uint16_t forceMaster ) {
    uint16_t regData = regRead(phyAddr, 1, 0x0834);
    if (0x0 != forceMaster)
    {
    	regData |= 0x4000;
    }
    else
    {
    	regData &= 0xBFFF;
    }
    regWrite(phyAddr, 1, 0x0834, regData);
}

uint16_t EsuPhy2112::getMasterSlave ( uint16_t phyAddr ) {
    return ( (regRead(phyAddr, 7, 0x8001) >> 14) & 0x0001 );
}

uint16_t EsuPhy2112::getModelNum ( uint16_t phyAddr ) {
    uint16_t modelNum = regRead(phyAddr, 1, 0x0003);
    return ( (modelNum & 0x03F0) >> 4 );
}

void EsuPhy2112::_applyQ2112GeSetting ( uint16_t phyAddr, uint16_t isAneg ) {
    uint16_t regData = 0;

	delay_ms(2);
    if (0x0 != isAneg)
        regWrite(phyAddr, 7, 0x0200, MRVL_88Q2112_AN_ENABLE | MRVL_88Q2112_AN_RESTART);
    else
        regWrite(phyAddr, 7, 0x0200, MRVL_88Q2112_AN_DISABLE);


        regWrite(phyAddr, 1, 0x0900, 0x4000);

        regData = regRead(phyAddr, 1, 0x0834);
        regData = (regData & 0xFFF0) | 0x0001;
        regWrite(phyAddr, 1, 0x0834, regData);

        regWrite(phyAddr, 3, 0xFFE4, 0x07B5);
        regWrite(phyAddr, 3, 0xFFE4, 0x06B6);
        delay_ms(5);

        regWrite(phyAddr, 3, 0xFFDE, 0x402F);
        regWrite(phyAddr, 3, 0xFE2A, 0x3C3D);
        regWrite(phyAddr, 3, 0xFE34, 0x4040);
        regWrite(phyAddr, 3, 0xFE4B, 0x9337);
        regWrite(phyAddr, 3, 0xFE2A, 0x3C1D);
        regWrite(phyAddr, 3, 0xFE34, 0x0040);
        regWrite(phyAddr, 7, 0x8032, 0x0064);
        regWrite(phyAddr, 7, 0x8031, 0x0A01);
        regWrite(phyAddr, 7, 0x8031, 0x0C01);
        regWrite(phyAddr, 3, 0xFE0F, 0x0000);
        regWrite(phyAddr, 3, 0x800C, 0x0000);
        regWrite(phyAddr, 3, 0x801D, 0x0800);
        regWrite(phyAddr, 3, 0xFC00, 0x01C0);
        regWrite(phyAddr, 3, 0xFC17, 0x0425);
        regWrite(phyAddr, 3, 0xFC94, 0x5470);
        regWrite(phyAddr, 3, 0xFC95, 0x0055);
        regWrite(phyAddr, 3, 0xFC19, 0x08D8);
        regWrite(phyAddr, 3, 0xFC1a, 0x0110);
        regWrite(phyAddr, 3, 0xFC1b, 0x0A10);
        regWrite(phyAddr, 3, 0xFC3A, 0x2725);
        regWrite(phyAddr, 3, 0xFC61, 0x2627);
        regWrite(phyAddr, 3, 0xFC3B, 0x1612);
        regWrite(phyAddr, 3, 0xFC62, 0x1C12);
        regWrite(phyAddr, 3, 0xFC9D, 0x6367);
        regWrite(phyAddr, 3, 0xFC9E, 0x8060);
        regWrite(phyAddr, 3, 0xFC00, 0x01C8);
        regWrite(phyAddr, 3, 0x8000, 0x0000);
        regWrite(phyAddr, 3, 0x8016, 0x0011);

        regWrite(phyAddr, 3, 0xFDA3, 0x1800);
        regWrite(phyAddr, 3, 0xFE02, 0x00C0);
        regWrite(phyAddr, 3, 0xFFDB, 0x0010);
        regWrite(phyAddr, 3, 0xFFF3, 0x0020);
        regWrite(phyAddr, 3, 0xFE40, 0x00A6);

        regWrite(phyAddr, 3, 0xFE60, 0x0000);
        regWrite(phyAddr, 3, 0xFE04, 0x0008);
        regWrite(phyAddr, 3, 0xFE2A, 0x3C3D);
        regWrite(phyAddr, 3, 0xFE4B, 0x9334);

        regWrite(phyAddr, 3, 0xFC10, 0xF600);
        regWrite(phyAddr, 3, 0xFC11, 0x073D);
        regWrite(phyAddr, 3, 0xFC12, 0x000D);
        regWrite(phyAddr, 3, 0xFC13, 0x0010);

}

uint16_t EsuPhy2112::loadOperateModeSetting(uint16_t phyAddr)
{
	if(phyAddr > MAX_PHY_NUM)
	{
		return MRVL_88Q2112_MODE_ERROR;
	}
	return operate_mode[phyAddr];
}

char EsuPhy2112::applyOperateModeConfig ( uint16_t phyAddr ) {
    uint16_t opMode = 0;
    char result = FALSE;

        opMode = loadOperateModeSetting(phyAddr);
        if (MRVL_88Q2112_MODE_ERROR != opMode) {
            if (MRVL_88Q2112_MODE_LEGACY == opMode) {
                // Enable 1000 BASE-T1 legacy mode support
                regWrite(phyAddr, 3, 0xFDB8, 0x0001);
                regWrite(phyAddr, 3, 0xFD3D, 0x0C14);
            }
            else {
                // Set back to default compliant mode setting
                regWrite(phyAddr, 3, 0xFDB8, 0x0000);
                regWrite(phyAddr, 3, 0xFD3D, 0x0000);
            }
            regWrite(phyAddr, 1, 0x0902, opMode | MRVL_88Q2112_MODE_ADVERTISE);
            result = TRUE;
        }


    return result;
}

void EsuPhy2112::_applyQ2112GeSoftReset(uint16_t phyAddr) {
    uint16_t regDataAuto = 0;

        if (getAnegEnabled(phyAddr)) {
            regWrite(phyAddr, 3, 0xFFF3, 0x0024);
        }
        //enable low-power mode
        regDataAuto = regRead(phyAddr, 1, 0x0000);
        regWrite(phyAddr, 1, 0x0000, regDataAuto | 0x0800);

        regWrite(phyAddr, 3, 0xFFF3, 0x0020);
        regWrite(phyAddr, 3, 0xFFE4, 0x000C);
        delay_ms(1);

        regWrite(phyAddr, 3, 0xffe4, 0x06B6);

        // disable low-power mode
        regWrite(phyAddr, 1, 0x0000, regDataAuto & 0xF7FF);
        delay_ms(1);

        regWrite(phyAddr, 3, 0xFC47, 0x0030);
        regWrite(phyAddr, 3, 0xFC47, 0x0031);
        regWrite(phyAddr, 3, 0xFC47, 0x0030);
        regWrite(phyAddr, 3, 0xFC47, 0x0000);
        regWrite(phyAddr, 3, 0xFC47, 0x0001);
        regWrite(phyAddr, 3, 0xFC47, 0x0000);

        regWrite(phyAddr, 3, 0x0900, 0x8000);

        regWrite(phyAddr, 1, 0x0900, 0x0000);
        regWrite(phyAddr, 3, 0xFFE4, 0x000C);
}

char EsuPhy2112::initQ2112Ge ( uint16_t phyAddr ) {
    _applyQ2112GeSetting(phyAddr, MRVL_88Q2112_AN_DISABLE);
    if (!applyOperateModeConfig(phyAddr))
        return FALSE;
    _applyQ2112GeSoftReset(phyAddr);
    return TRUE;
}

int EsuPhy2112::setupForcedSpeedDuringLinkup(uint16_t phyAddr, uint16_t targetSpeed)
{
	if (getAnegEnabled(phyAddr)) return FALSE;

	//Set target speed
	regWrite(phyAddr, 1, 0x0000, regRead(phyAddr, 1, 0x0000) | 0x0800);
	delay_ms(10);
	if (MRVL_88Q2112_1000BASE_T1 == targetSpeed) {
		regWrite(phyAddr, 1, 0x0834, (regRead(phyAddr, 1, 0x0834) & 0xFFF0) | 0x0001);
		regWrite(phyAddr, 3, 0xffe4, 0x07B6);
	}
	else {
		regWrite(phyAddr, 1, 0x0834, regRead(phyAddr, 1, 0x0834) & 0xFFF0);
	}
	regWrite(phyAddr, 1, 0x0000, regRead(phyAddr, 1, 0x0000) & 0xF7FF);
	delay_ms(1);

	//apply the init script according to target speed.
	if (MRVL_88Q2112_1000BASE_T1 == targetSpeed) {
		 return initQ2112Ge(phyAddr);
	}
	else {
		initQ2112Fe(phyAddr);
	}

	return TRUE;
}

char EsuPhy2112::saveOperateModeSetting(uint16_t phyAddr, uint16_t modeId)
{
	if(phyAddr > MAX_PHY_NUM)
	{
		return FALSE;
	}
	operate_mode[phyAddr] = modeId;
	return TRUE;
}

char EsuPhy2112::manualSetOperateModeSetting ( uint16_t phyAddr, uint16_t selfMode ) {
        return saveOperateModeSetting(phyAddr, selfMode & MRVL_88Q2112_MODE_MSK);
}

char EsuPhy2112::initQ2112Ge_SingleSendS(uint16_t phyAddr)
{
	_applyQ2112GeSetting(phyAddr, MRVL_88Q2112_AN_DISABLE);

	regWrite(phyAddr, 7, 0x8032, 0x0);
	regWrite(phyAddr, 7, 0x8031, 0xA1F);
	regWrite(phyAddr, 7, 0x8031, 0xC1F);
	regWrite(phyAddr, 7, 0x8032, 0x1);
	regWrite(phyAddr, 7, 0x8031, 0xA1B);
	regWrite(phyAddr, 7, 0x8031, 0xC1B);
	regWrite(phyAddr, 7, 0x8032, 0xB);
	regWrite(phyAddr, 7, 0x8031, 0xA1C);
	regWrite(phyAddr, 7, 0x8031, 0xC1C);
	regWrite(phyAddr, 3, 0x800C, 0x8);
	regWrite(phyAddr, 7, 0x8035, 0x27B8);
	regWrite(phyAddr, 7, 0x8036, 0x25);

	if (0 != getMasterSlave(phyAddr)) {
		regWrite(phyAddr, 7, 0x8032, 0x5A);
		regWrite(phyAddr, 7, 0x8031, 0xA01);
		regWrite(phyAddr, 7, 0x8031, 0xC01);
		regWrite(phyAddr, 7, 0x8032, 0x1D);
		regWrite(phyAddr, 7, 0x8031, 0xA22);
		regWrite(phyAddr, 7, 0x8031, 0xC22);
		regWrite(phyAddr, 3, 0xFE04, 0x8);
	}
	else {
		regWrite(phyAddr, 7, 0x8032, 0x64);
        regWrite(phyAddr, 7, 0x8031, 0xA01);
        regWrite(phyAddr, 7, 0x8031, 0xC01);
        regWrite(phyAddr, 7, 0x8032, 0x1A);
        regWrite(phyAddr, 7, 0x8031, 0xA22);
        regWrite(phyAddr, 7, 0x8031, 0xC22);
        regWrite(phyAddr, 3, 0xFE04, 0x10);
	}

	if (!applyOperateModeConfig(phyAddr))
		return FALSE;
	_applyQ2112GeSoftReset(phyAddr);
	return TRUE;
}

void EsuPhy2112::_applyQ2112AnegSetting(uint16_t phyAddr) {
    uint16_t regDataAuto = 0;

        regDataAuto = regRead(phyAddr, 7, 0x8032);
        regWrite(phyAddr, 7, 0x8032, regDataAuto | 0x0001);
        regWrite(phyAddr, 7, 0x8031, 0x0013);
        regWrite(phyAddr, 7, 0x8031, 0x0a13);

        regDataAuto = regRead(phyAddr, 7, 0x8032);
        regDataAuto = (0xFC00 & regDataAuto) | 0x0012;
        regWrite(phyAddr, 7, 0x8032, regDataAuto);
        regWrite(phyAddr, 7, 0x8031, 0x0016);
        regWrite(phyAddr, 7, 0x8031, 0x0a16);

        regDataAuto = regRead(phyAddr, 7, 0x8032);
        regDataAuto = (0xFC00 & regDataAuto) | 0x64;
        regWrite(phyAddr, 7, 0x8032, regDataAuto);
        regWrite(phyAddr, 7, 0x8031, 0x0017);
        regWrite(phyAddr, 7, 0x8031, 0x0a17);

        regWrite(phyAddr, 3, 0x800C, 0x0008);
        regWrite(phyAddr, 3, 0xFE04, 0x0016);
}

int EsuPhy2112::setAneg ( uint16_t phyAddr, uint16_t forceMaster, uint16_t flags ) {
    uint16_t regDataAuto, anAbility = 0;

    if (0x0 != forceMaster)
        anAbility = MRVL_88Q2112_AN_PREFER_MASTER;

    // Disable AN if no data rates supported
    if (0x0 == (flags & (MRVL_88Q2112_AN_GE_ABILITY | MRVL_88Q2112_AN_FE_ABILITY))) {
        regWrite(phyAddr, 7, 0x0203, MRVL_88Q2112_AN_DISABLE);
        regWrite(phyAddr, 7, 0x0202, 0x0001);
        regWrite(phyAddr, 7, 0x0200, MRVL_88Q2112_AN_RESET);
        return 1;
    }

    // Initialize for 1 or more data rates
    if (0x0 != (flags & MRVL_88Q2112_AN_GE_ABILITY)) {
        _applyQ2112GeSetting(phyAddr, MRVL_88Q2112_AN_ENABLE);
        if (!applyOperateModeConfig(phyAddr))
            return 0;
        anAbility |= MRVL_88Q2112_AN_GE_ABILITY;
    }

    if (0x0 != (flags & MRVL_88Q2112_AN_FE_ABILITY)) {
        _applyQ2112FeSetting(phyAddr, MRVL_88Q2112_AN_ENABLE);
        anAbility |= MRVL_88Q2112_AN_FE_ABILITY;
    }

    _applyQ2112AnegSetting(phyAddr);

    // Set Ability registers
    regWrite(phyAddr, 7, 0x0203, anAbility);

    // Force master if needed
    regDataAuto = regRead(phyAddr, 7, 0x0202);
    if (0x0 != forceMaster)
        regDataAuto |= 0x1000;
    else
        regDataAuto &= 0xEFFF;
    regWrite(phyAddr, 7, 0x0202, regDataAuto);

    // Reset routines to restart Auto-Negotiation
    if (0x0 != (flags & MRVL_88Q2112_AN_GE_ABILITY))
        _applyQ2112GeSoftReset(phyAddr);
    if (0x0 != (flags & MRVL_88Q2112_AN_FE_ABILITY))
        _applyQ2112FeSoftReset(phyAddr);
    return 1;
}

// Get real time PMA link status
// @param phyAddr bootstrap address of the PHY
// @return true if link is up, false otherwise
unsigned char EsuPhy2112::getRealTimeLinkStatus ( uint16_t phyAddr ) {
    uint16_t retData1 = 0;
	retData1 = getSpeed(phyAddr);
    if (MRVL_88Q2112_1000BASE_T1 == getSpeed(phyAddr)) {
        retData1 = regRead(phyAddr, 3, 0x0901);
        retData1 = regRead(phyAddr, 3, 0x0901);    // Read twice: register latches low value
    }
    else {
        retData1 = regRead(phyAddr, 3, 0x8109);
    }
    return (0x0 != (retData1 & 0x0004));
}

// Signal Quality Indicator (SQI) with 16 levels (15 is best quality)
// @param phyAddr bootstrap address of the PHY
// @return quality indicator
uint16_t EsuPhy2112::getSQIReading_16Level ( uint16_t phyAddr ) {
    uint16_t SQI = 0;
    uint16_t regVal = 0;

    //check link
    if (!getRealTimeLinkStatus(phyAddr))
        return 0;

    // read different register for different rates
    if (MRVL_88Q2112_100BASE_T1 == getSpeed(phyAddr)) {  // 100 BASE-T1
        regVal = regRead(phyAddr, 3, 0x8230);
        SQI = (regVal & 0xF000) >> 12;
    }
    else
    {  // 1000 BASE-T1
			// change sqi offset

			regVal = regRead(phyAddr, 3, 0xFC5D);
			regWrite(phyAddr, 3, 0xFC5D, (regVal & 0xFF00) | 0x00AC);

			// SQI register
			SQI = regRead(phyAddr, 3, 0xFC88) & 0xF;

			if (!getRealTimeLinkStatus(phyAddr)) {
				SQI = 0;
			}
    }
    return SQI;
}

// Cable Quality Indicator (CQI) providing Insertion Loss (IL) and Return Loss (RL)
// @param phyAddr bootstrap address of the PHY
// @return true when measurement complete, false if link down or on error
unsigned char EsuPhy2112::getCQIReading ( uint16_t phyAddr, uint16_t *IL, uint16_t *RL ) {
    uint16_t regVal = 0;
    uint16_t rb_orig = 0;
    
    //check link
    if (!getRealTimeLinkStatus(phyAddr))
        return FALSE;

    // procedure differs between data rates
    if (MRVL_88Q2112_100BASE_T1 == getSpeed(phyAddr)) {  // 100 BASE-T1

        short cqi_il_offset = 16;
        short cqi_rl_offset = -29;
        int cnt = 0;

        rb_orig = regRead(phyAddr, 3, 0xfb80);
        regWrite(phyAddr, 3, 0xfb80, rb_orig & 0xfff7);

        regVal = (regRead(phyAddr, 3, 0xfbee) & 0xfff0) | 0x000a;
        regWrite(phyAddr, 3, 0xfbee, regVal);
        regWrite(phyAddr, 3, 0xfbe8, 0xd08e);
        regWrite(phyAddr, 3, 0xfbe9, 0x3001);
        regWrite(phyAddr, 3, 0xfbea, 0x774f);
        regWrite(phyAddr, 3, 0xfbeb, 0x1040);

        cqi_il_offset = cqi_il_offset * 2;
        if (cqi_il_offset<0) {
            cqi_il_offset = cqi_il_offset + 256;
        }
        regWrite(phyAddr, 3, 0xfbec, 64 + cqi_il_offset * 256);
        regWrite(phyAddr, 3, 0xfbed, 0x783c);

        regVal = (regRead(phyAddr, 3, 0xfbef) & 0xFF00) | 0x0018;
        regWrite(phyAddr, 3, 0xfbef, regVal);

        cqi_rl_offset = cqi_rl_offset * 2;
        if (cqi_rl_offset<0) {
            cqi_rl_offset = cqi_rl_offset + 256;
        }
        regWrite(phyAddr, 3, 0xfbf0, 64 + cqi_rl_offset * 256);


        regVal = regRead(phyAddr, 3, 0xfbee) | 0x0030;
        regWrite(phyAddr, 3, 0xfbee, regVal);
        regWrite(phyAddr, 3, 0xfbee, regVal & 0xffcf);
        regWrite(phyAddr, 3, 0xfbee, regVal);

        regVal = regRead(phyAddr, 3, 0xfb80);
        regWrite(phyAddr, 3, 0xfb80, regVal | 0x0008);

        regVal = regRead(phyAddr, 3, 0xfba1);
        regWrite(phyAddr, 3, 0xfba1, regVal | 0x0001);
        regVal = regRead(phyAddr, 3, 0xfb94);
        regWrite(phyAddr, 3, 0xfb94, regVal | 0x0001);

        while (TRUE) {
            regVal = regRead(phyAddr, 3, 0xfbf4);
            if (0x0 == (regVal & 0x3000))
                break;
            delay_ms(10);
            cnt++;
            if (cnt > 100)
                break;
        }


        regVal = regRead(phyAddr, 3, 0xfbf4);
        *IL = (regVal & 0x00f0) >> 4;
        *RL = (regVal & 0x0f00) >> 8;


        regVal = regRead(phyAddr, 3, 0xfba1);
        regWrite(phyAddr, 3, 0xfba1, regVal & 0xfffe);

        regVal = regRead(phyAddr, 3, 0xfb94);
        regWrite(phyAddr, 3, 0xfb94, regVal & 0xfffe);

        regVal = regRead(phyAddr, 3, 0xfbee);
        regWrite(phyAddr, 3, 0xfbee, regVal & 0xfffe);


        regWrite(phyAddr, 3, 0xfb80, rb_orig);
    }
    else {  // 1000 BASE-T1

        short cqi_il_offset = 31;
        short cqi_rl_offset = -26;

        rb_orig = regRead(phyAddr, 3, 0xfc00);
        regWrite(phyAddr, 3, 0xfc00, rb_orig & 0xfff7);

        cqi_il_offset = cqi_il_offset * 2;
        if (cqi_il_offset<0) {
            cqi_il_offset = cqi_il_offset + 256;
        }
        regWrite(phyAddr, 3, 0xfc64, 63 + cqi_il_offset * 256);

        cqi_rl_offset = cqi_rl_offset * 4;
        if (cqi_rl_offset<0) {
            cqi_rl_offset = cqi_rl_offset + 256;
        }
        regWrite(phyAddr, 3, 0xfc7c, 64 + cqi_rl_offset * 256);


        regVal = regRead(phyAddr, 3, 0xfc16);
        regWrite(phyAddr, 3, 0xfc16, regVal | 0x0001);


        regVal = regRead(phyAddr, 3, 0xfc89);
        regWrite(phyAddr, 3, 0xfc89, regVal & 0xFFED);
        regWrite(phyAddr, 3, 0xfc89, regVal | 0x0012);

        delay_ms(500);


        regVal = regRead(phyAddr, 3, 0xfc00);
        regWrite(phyAddr, 3, 0xfc00, regVal | 0x0008);


        regVal = regRead(phyAddr, 3, 0xfc66);
        *IL = (regVal & 0xf000) >> 12;
        regVal = regRead(phyAddr, 3, 0xfc80);
        *RL = regVal / 64;

        if (*IL > 15) *IL = 15;
        if (*RL > 15) *RL = 15;


        regVal = regRead(phyAddr, 3, 0xfc16);
        regWrite(phyAddr, 3, 0xfc16, regVal & 0xFFFE);


        regWrite(phyAddr, 3, 0xfc00, rb_orig);
    }
    return TRUE;
}


// Virtual Cable Test (VCT) intended to find faults in cables and improper termination
// @param phyAddr bootstrap address of the PHY
// @param &distanceToFault if test successful, distance to fault will be stored here
// @param &cable_status if test successful, cable status will be stored here
// @return true when measurement complete, false if test failed
//     In case method returns true, interpret results as follows:
//     cable_status     distanceToFault     result
//     't'              0                   proper termination
//     'o'              value               open circuit at length "value"
//     's'              value               short circuit at length "value"
//     No other results possible
unsigned char EsuPhy2112::getVCTReading ( uint16_t phyAddr, int *distanceToFault, char *cable_status ) {
    uint16_t reg_fec9 = 0;
    uint16_t retData = 0;
    uint16_t cnt = 0;
    uint16_t vct_dis = 0;
    uint16_t vct_amp = 0;
    uint16_t vct_polarity = 0;
    // Ignore Wire Activity
    reg_fec9 = regRead(phyAddr, 3, 0xfec9);
    regWrite(phyAddr, 3, 0xfec9, reg_fec9 | 0x0080);

    // Set incoming ADC sign bit
    retData = regRead(phyAddr, 3, 0xfec3);
    regWrite(phyAddr, 3, 0xfec3, retData | 0x2000);

    retData = regRead(phyAddr, 3, 0xfe5d);
    regWrite(phyAddr, 3, 0xfe5d, retData & 0xEFFF);

    // Adjust thresholds
    regWrite(phyAddr, 3, 0xfec4, 0x0f1f);
    regWrite(phyAddr, 3, 0xfec7, 0x1115);

    // Enable TDR (self-clear register)
    retData = regRead(phyAddr, 3, 0xfec3);
    regWrite(phyAddr, 3, 0xfec3, retData | 0x4000);
    delay_ms(210); //delay 210ms

    // Check if test is done
    while (TRUE) {
        //make sure previous VCT is done
        retData = regRead(phyAddr, 3, 0xFEDB);
        if (0x0 == (retData & 0x0400))   //done = 0x0400, busy = 0
        	delay_ms(20); //delay 20ms
        else
            break;  // test done
        cnt++;
        if (cnt > 20) { // Timeout error condition
            regWrite(phyAddr, 3, 0xfec9, reg_fec9);
            return FALSE;
        }
    }
    
    // Capture results
    retData = regRead(phyAddr, 3, 0xfedb);
    vct_dis = retData & 0x03ff;
    retData = regRead(phyAddr, 3, 0xfedc);
    vct_amp = retData & 0x007f;
    vct_polarity = retData & 0x0080;

    // process results
    if (0x0 == vct_dis) {
    	*distanceToFault = 0;
    }
    else {
    	*distanceToFault = (vct_dis - 39) * 12931;    ////Magnify 10,0000 times
    }
    if ((0x0 == vct_dis) && (0x0 != vct_polarity)) {
    	*cable_status = 't';
    }
    else if (0x0 != vct_polarity) {
    	*cable_status = 'o';
    }
    else {
    	*cable_status = 's';
    }

    // stop test
    regWrite(phyAddr, 3, 0xfec9, reg_fec9);
    return TRUE;
}


uint16_t EsuPhy2112::phyInit ( uint16_t phyAddr, uint16_t flags)
{
    char smi_ok = FALSE;
	char linkup_before_set_mode = FALSE;
	int smi_check_cnt = 3;
	while(smi_check_cnt--)
	{
        if (MRVL_88Q2112_PRODUCT_ID == getModelNum(phyAddr)) {
            // SMI success - proceed to config
            smi_ok = TRUE;
            break;
        }
        delay_ms(1);
	}
    if (!smi_ok)    // ERROR: failed to establish SMI access
    {	
		cout << "SMI access failed" << endl;
        return MRVL_88Q2112_INIT_ERROR_SMI;
    }
    // 2) Init Sequence
    setMasterSlave(phyAddr, MRVL_88Q2112_INIT_MASTER & flags);

	if (checkLink(phyAddr))
	{
		linkup_before_set_mode = TRUE;
	}
	if (0x0 != (MRVL_88Q2112_INIT_GE & flags)) {
		// 1000 BASE-T1: Detect PHY revision, save initial compatibility mode (Default/Legacy), and Initialize.
		if (!manualSetOperateModeSetting(phyAddr, MRVL_88Q2112_MODE_DEFAULT))
		{
			cout << "Failed to save mode" << endl;
			return MRVL_88Q2112_INIT_ERROR_SAVE_MODE;
		}
		if (linkup_before_set_mode) {
			//If the link is up before init mode, appy setupForcedSpeedDuringLinkup
			if(!setupForcedSpeedDuringLinkup(phyAddr, MRVL_88Q2112_1000BASE_T1))
			{
				cout << "Failed to setup forced speed during linkup" << endl;
				return MRVL_88Q2112_INIT_ERROR_INIT_GE;
			}
		}
		else {
			//If the link is down, appy init script directly
			if (MRVL_88Q2112_SINGLE_SEND_S_ENABLE) {
				// option to enable single send_s
				if (!initQ2112Ge_SingleSendS(phyAddr))
				{
					cout << "Failed to init single send_s" << endl;
					return MRVL_88Q2112_INIT_ERROR_INIT_GE;
				}
			}
			else {
				// option to use default send_s settings
				if (!initQ2112Ge(phyAddr))
				{
					cout << "Failed to init default send_s" << endl;
					return MRVL_88Q2112_INIT_ERROR_INIT_GE;
				}
			}
			if (!initQ2112Ge(phyAddr))
				return MRVL_88Q2112_INIT_ERROR_INIT_GE;
		}

	}
	else if (0x0 != (MRVL_88Q2112_INIT_FE & flags)) {
		// 100 BASE-T1 Initialization
		if (linkup_before_set_mode) {
			//If the link is up before init mode, appy setupForcedSpeedDuringLinkup
			setupForcedSpeedDuringLinkup(phyAddr, MRVL_88Q2112_100BASE_T1);
		}
		else {
			//If the link is down, appy init script directly
			initQ2112Fe(phyAddr);
		}

	}
	else {
		cout << "Invalid arguments" << endl;
		return MRVL_88Q2112_INIT_ERROR_ARGS;
	}

	if (checkLink(phyAddr)) {
		// link up, exit with success
		cout << "Link is up" << endl;
	}
	else {
		cout << "Link is down" << endl;
	}
    return MRVL_88Q2112_INIT_DONE;
}

int EsuPhy2112::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_WR ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	ioc_data.data = data ;
    int ret = Ioctl(ioc_cmd, (caddr_t) &ioc_data);
    usleep(100);
	return ret;
}

int EsuPhy2112::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_RD ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	err =  Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
	*data = ioc_data.data ;
    usleep(100);
	return err;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuStat::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuPhy2112::postAction(void)
{ 
	return err ;
}

uint16_t EsuPhy2112::regRead(uint16_t phyAddr, uint16_t devAddr, uint16_t regAddr) {
    uint32_t data;
    uint16_t opcode0 = (0xa000 & 0xFC00) | (phyAddr << 5) | devAddr;
    uint16_t opcode1 = (0xac00 & 0xFC00) | (phyAddr << 5) | devAddr;
	WriteIO(0x1C, 0x19, regAddr);
	WriteIO(0x1C, 0x18, opcode0);
	WriteIO(0x1C, 0x18, opcode1);
	ReadIO(0x1C, 0x19, &data);
    return (uint16_t)data;
}

void EsuPhy2112::regWrite(uint16_t phyAddr, uint16_t devAddr, uint16_t regAddr, uint16_t data) {
    uint16_t opcode0 = (0xa000 & 0xFC00) | (phyAddr << 5) | devAddr;
    uint16_t opcode1 = (0xa400 & 0xFC00) | (phyAddr << 5) | devAddr;
	WriteIO(0x1C, 0x19, regAddr);
	WriteIO(0x1C, 0x18, opcode0);
	WriteIO(0x1C, 0x19, (uint32_t)data);
	WriteIO(0x1C, 0x18, opcode1);
}

void EsuPhy2112::delay_ms(uint16_t delayTime) {
	usleep(delayTime * 1000);
}



struct PortStatus {
    bool phyDetected;
    bool linkUp;         
    uint16_t speed;      
};

void EsuPhy2112::getPortState(uint32_t port) {
    uint32_t regData;
    ReadIO(port, 0x00, &regData);
    
    PortStatus status;
    status.phyDetected = (regData >> 12) & 0x1;    // 第12位
    status.linkUp = (regData >> 11) & 0x1;         // 第11位
    
    // 解析速率 (9:8位)
    uint16_t speedBits = (regData >> 8) & 0x3;
    switch(speedBits) {
        case 0x2:
            status.speed = 1000;    // 1000M
            break;
        case 0x1:
            status.speed = 100;     // 100M
            break;
        default:
            status.speed = 0;       // 未知或其他速率
    }
    
    // 打印状态信息
    cout << "Port " << port << " Status:" << endl;
    cout << "  PHY Detected: " << (status.phyDetected ? "Yes" : "No") << endl;
    cout << "  Link Status: " << (status.linkUp ? "Up" : "Down") << endl;
    cout << "  Speed: " << status.speed << "M" << endl;
}

#ifndef MAX_INIT_ATTEMPTS
#define MAX_INIT_ATTEMPTS 3
#endif

int EsuPhy2112::cycle_phyInit(uint16_t phy_addr, uint16_t config_param) {
    int err = 0;
    for (int attempt = 0; attempt < MAX_INIT_ATTEMPTS; attempt++) {
        err = phyInit(phy_addr, config_param);
        delay_ms(100);
        
        if (err == MRVL_88Q2112_INIT_DONE) {
            cout << "PHY initialization done at address " << phy_addr << " (attempt " << attempt + 1 << ")" << endl;
            return IOCTL_CMD_STATUS_OK;
        }
        
        cout << "PHY initialization failed at address " << phy_addr << " (attempt " << attempt + 1 << ")" << endl;
        if (attempt < MAX_INIT_ATTEMPTS - 1) {
            cout << "Retrying..." << endl;
            delay_ms(500);
        }
    }
    return IOCTL_CMD_STATUS_ERROR;
}

struct PhyCommand {
    uint32_t dev;
    uint32_t reg;
    uint32_t value;
};

int EsuPhy2112::DoInit(void) {
    
    int err = IOCTL_CMD_STATUS_OK;

    static const PhyCommand initSequence[] = {
        {0x1C, 0x1A, 0xE31F},
        {0x1C, 0x1A, 0xE540},
        {0x9,  0x00, 0x0e0a},
        {0xa,  0x00, 0x0e0a}
    };
    for (const auto& cmd : initSequence) {
        err = WriteIO(cmd.dev, cmd.reg, cmd.value);
		usleep(100);
        if (err != IOCTL_CMD_STATUS_OK) {
            return err;
        }
    }

    uint16_t config_param = 0x0000;
	if (master_slave == 1) {
		config_param |= MRVL_88Q2112_INIT_MASTER;
	} else {
		config_param |= MRVL_88Q2112_AN_ENABLE;
	}
	if (link_speed == 1) {
		config_param |= MRVL_88Q2112_INIT_GE;
	} else {
		config_param |= MRVL_88Q2112_INIT_FE;
	}
    if (phy_addr == -1) {
        for (int i = 0; i <= 7; i++) {
            if (cycle_phyInit(i, config_param) != IOCTL_CMD_STATUS_OK) {
                err = IOCTL_CMD_STATUS_ERROR;
                continue;
            }
        }
        return err;
    }
    else {
        return cycle_phyInit(phy_addr, config_param);
    }
}
uint16_t EsuPhy2112::setSpeed(uint16_t phyAddr, uint16_t speed) {
    if (speed == 1) {
		// 1000 BASE-T1: Detect PHY revision, save initial compatibility mode (Default/Legacy), and Initialize.
		if (!manualSetOperateModeSetting(phyAddr, MRVL_88Q2112_MODE_DEFAULT))
		{
			cout << "Failed to save mode" << endl;
			return MRVL_88Q2112_INIT_ERROR_SAVE_MODE;
		}
		if (checkLink(phyAddr)) {
			//If the link is up before init mode, appy setupForcedSpeedDuringLinkup
			if(!setupForcedSpeedDuringLinkup(phyAddr, MRVL_88Q2112_1000BASE_T1))
			{
				cout << "Failed to setup forced speed during linkup" << endl;
				return MRVL_88Q2112_INIT_ERROR_INIT_GE;
			}
		}
		else {
			//If the link is down, appy init script directly
			if (MRVL_88Q2112_SINGLE_SEND_S_ENABLE) {
				// option to enable single send_s
				if (!initQ2112Ge_SingleSendS(phyAddr))
				{
					cout << "Failed to init single send_s" << endl;
					return MRVL_88Q2112_INIT_ERROR_INIT_GE;
				}
			}
			else {
				// option to use default send_s settings
				if (!initQ2112Ge(phyAddr))
				{
					cout << "Failed to init default send_s" << endl;
					return MRVL_88Q2112_INIT_ERROR_INIT_GE;
				}
			}
			if (!initQ2112Ge(phyAddr))
				return MRVL_88Q2112_INIT_ERROR_INIT_GE;
		}

	}
	else {
		// 100 BASE-T1 Initialization
		if (checkLink(phyAddr)) {
			//If the link is up before init mode, appy setupForcedSpeedDuringLinkup
			setupForcedSpeedDuringLinkup(phyAddr, MRVL_88Q2112_100BASE_T1);
		}
		else {
			//If the link is down, appy init script directly
			initQ2112Fe(phyAddr);
		}

	}
    return IOCTL_CMD_STATUS_OK;
}

int EsuPhy2112::DoSet(void) {
    setMasterSlave(phy_addr, master_slave);
    err = setSpeed(phy_addr, link_speed);
    return err;
}

int EsuPhy2112::DoGet(void) {
	uint16_t link_status;
	link_status = checkLink(phy_addr);
	cout << "PHY Link Status: " << (link_status == 1 ? "Connected" : "Disconnected") << endl;
	
	uint16_t ms_status;
	ms_status = getMasterSlave(phy_addr);
	cout << "PHY Master/Slave Status: " << (ms_status == 1 ? "Master" : "Slave") << endl;

	uint16_t speed;
	speed = getSpeed(phy_addr);
	cout << "PHY Link Speed: " << (speed == 1 ? "1000 Mbps" : "100 Mbps") << endl;

	getPortState(internal_port);
	return err;
}

int EsuPhy2112::DoDiag(void) {
	uint16_t sqi = getSQIReading_16Level(phy_addr);
	cout << "PHY SQI: " << sqi << endl;

	uint16_t IL, RL;
	uint16_t cqi = getCQIReading(phy_addr, &IL, &RL);
	cout << "IL: " << IL << ", RL: " << RL << endl;

	int distanceToFault;
	char cable_status;
	uint16_t vct = getVCTReading(phy_addr, &distanceToFault, &cable_status);
	cout << "distanceToFault: " << distanceToFault << ", cable_status: " << cable_status << endl;

	return err;
}

int EsuPhy2112::Action(void)
{ 
	err = IOCTL_CMD_STATUS_ERROR ;

	if (ioc_cmd == OAK_IOCTL_REG_ESU_REQ && SetDevice(if_name) == true) {
		switch(sub_cmd) {
		case 1:
			err = DoSet() ;
			break ;
		case 0:
			err = DoGet() ;
			break ;
		case 3:
			err = DoDiag() ;
			break ;
		case 2:
			err = DoInit() ;
			break ;
		}
	}
	return (err) ;
}



/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuStat::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuPhy2112::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 2) {
		if (strcmp(argv[1], "PHY2112") == 0 || strcmp(argv[1], "phy2112") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
