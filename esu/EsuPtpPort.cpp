/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"
#include <ctime>
#include <iomanip>
#include <unistd.h>  // for usleep

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1,  1 },	// Interface name
        { "-port",	1,  1 },	// Port number (required)
        { "-list",	0,  0 },	// List PTP Port registers
        { "-rd",	0,  1 },	// Default: read operation
        { "-wr",	0,  2 },	// write operation
        { "-tsformat",	0,  1 },	// Set timestamp format: 0=32-bit global timer, 1=32-bit ToD
        { "-gettsformat", 0, 0 },	// Get current timestamp format
        { "-v",		0,  0 },	// verbose option
} ;

// PTP Port register definitions based on Figure 16: PTP Port Register Bit Map
// Global 2 Offset 0x16 & 0x17 w/AVBBlock = 0x0 & AVBPort = 11:1
static PTP_PORT_Field_t     R_00[] = {
	{ 15, 12,        "TransSpec",		0, },
	{ 11, 11,        "DT_DisableTranSpecCheck",	0, },
	{ 10, 10,        "PTPMACsecEn",	0, },
	{  9,  9,        "Preemption Timestamp select",	0, },
	{  8,  8,        "OneStepSync",	0, },
	{  7,  7,        "OneStepEgress",	0, },
	{  6,  6,        "OneStepIngress",	0, },
	{  5,  5,        "Mirror Mode for pass-through PTP frames",	0, },
	{  4,  4,        "TF_TimeStampFormat",	0, },
	{  3,  2,        "PTP Preemption Control",	0, },
	{  1,  1,        "Disable Time Stamp Counter Overwriting",	0, },
	{  0,  0,        "Disable Precise Time Stamp logic",		0, },
} ;

static PTP_PORT_Field_t     R_01[] = {
	{ 15, 14,        "RES_Reserved",	0, },
	{ 13,  8,        "IPJump",		0, },
	{  7,  5,        "AltET Jump",		0, },
	{  4,  0,        "ETJump",		0, },
} ;

static PTP_PORT_Field_t     R_02[] = {
	{ 15, 8,        "ArrTSMode",	0, },
	{ 7, 7,        "Filter LED Activity",	0, },
	{ 6, 6,        "Port PTP Hardware Acceleration enable",	0, },
	{ 5, 5,        "Keep Frame’s SA",	0, },
	{ 4, 3,        "Reserved",	0, },
	{ 2, 2,        "ExternalHardwareAccel",	0, },
	{ 1, 1,        "Precise Time Protocol Port Departure Interrupt enable",	0, },
	{ 0, 0,        "Precise Time Protocol Port Arrival Interrupt enable",	0, },
} ;

static PTP_PORT_Field_t     R_03[] = {
	{ 15,  8,        "ArrivalLEDControl",	0, },
	{  7,  0,        "DepartureLEDControl",	0, },
} ;

static PTP_PORT_Field_t     R_04[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_05[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_06[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_07[] = {
	{ 15, 15,        "U_Update",		0, },
	{ 14,  8,        "Pointer",		0, },
	{  7,  0,        "Data",		0, },
} ;

static PTP_PORT_Field_t     R_08[] = {
	{ 15,  3,        "Reserved",		0, },
	{  2,  1,        "Precise Time Protocol Arrival Time 0 Interrupt Status",	0, },
	{  0,  0,        "Precise Time Protocol Arrival 0 Time Valid",	0, },
} ;

static PTP_PORT_Field_t     R_09[] = {
	{ 15,  0,        "PTPArrival0Time_15_0",	0, },
} ;

static PTP_PORT_Field_t     R_0A[] = {
	{ 15,  0,        "PTPArrival0Time_31_16",	0, },
} ;

static PTP_PORT_Field_t     R_0B[] = {
	{ 15,  0,        "Precise Time Protocol Arrival 0 Sequence Identifier",	0, },
} ;

static PTP_PORT_Field_t     R_0C[] = {
	{ 15,  3,        "Reserved",		0, },
	{  2,  1,        "Precise Time Protocol Arrival Time 1 Interrupt Status",	0, },
	{  0,  0,        "Precise Time Protocol Arrival 1 Time Valid",	0, },
} ;

static PTP_PORT_Field_t     R_0D[] = {
	{ 15,  0,        "PTPArrival1Time_15_0",	0, },
} ;

static PTP_PORT_Field_t     R_0E[] = {
	{ 15,  0,        "PTPArrival1Time_31_16",	0, },
} ;

static PTP_PORT_Field_t     R_0F[] = {
	{ 15,  0,        "Precise Time Protocol Arrival 1 Sequence Identifier",	0, },
} ;

static PTP_PORT_Field_t     R_10[] = {
	{ 15,  3,        "Reserved",		0, },
	{  2,  1,        "Precise Time Protocol Departure Time Interrupt Status",	0, },
	{  0,  0,        "Precise Time Protocol Departure Time Valid",	0, },
} ;

static PTP_PORT_Field_t     R_11[] = {
	{ 15,  0,        "PTPDepartureTime_15_0",	0, },
} ;

static PTP_PORT_Field_t     R_12[] = {
	{ 15,  0,        "PTPDepartureTime_31_16",	0, },
} ;

static PTP_PORT_Field_t     R_13[] = {
	{ 15,  0,        "Precise Time Protocol Departure Sequence Identifier",	0, },
} ;

static PTP_PORT_Field_t     R_14[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_15[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_16[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_17[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_18[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_19[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_1A[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_1B[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_1C[] = {
	{ 15,  0,        "IngressMeanPathDelay",	0, },
} ;

static PTP_PORT_Field_t     R_1D[] = {
	{ 15,  15,        "Ingress Path Delay Asymmetry Sign",		0, },
	{ 14,  0,        "IngressPathDelayAsymmetry",	0, },
} ;

static PTP_PORT_Field_t     R_1E[] = {
	{ 15,  15,        "Egress Path Delay Asymmetry Sign",		0, },
	{ 14,  0,        "EgressPathDelayAsymmetry",	0, },
} ;

static PTP_PORT_Field_t     R_1F[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Register_t  PTP_PORT_REGS[] = {
        { 0, REG_FIELD_SIZE(R_00), R_00, 0, 0 },
        { 1, REG_FIELD_SIZE(R_01), R_01, 0, 0 },
        { 2, REG_FIELD_SIZE(R_02), R_02, 0, 0 },
        { 3, REG_FIELD_SIZE(R_03), R_03, 0, 0 },
        { 4, REG_FIELD_SIZE(R_04), R_04, 0, 0 },
        { 5, REG_FIELD_SIZE(R_05), R_05, 0, 0 },
        { 6, REG_FIELD_SIZE(R_06), R_06, 0, 0 },
        { 7, REG_FIELD_SIZE(R_07), R_07, 0, 0 },
        { 8, REG_FIELD_SIZE(R_08), R_08, 0, 0 },
        { 9, REG_FIELD_SIZE(R_09), R_09, 0, 0 },
        {10, REG_FIELD_SIZE(R_0A), R_0A, 0, 0 },
        {11, REG_FIELD_SIZE(R_0B), R_0B, 0, 0 },
        {12, REG_FIELD_SIZE(R_0C), R_0C, 0, 0 },
        {13, REG_FIELD_SIZE(R_0D), R_0D, 0, 0 },
        {14, REG_FIELD_SIZE(R_0E), R_0E, 0, 0 },
        {15, REG_FIELD_SIZE(R_0F), R_0F, 0, 0 },
        {16, REG_FIELD_SIZE(R_10), R_10, 0, 0 },
        {17, REG_FIELD_SIZE(R_11), R_11, 0, 0 },
        {18, REG_FIELD_SIZE(R_12), R_12, 0, 0 },
        {19, REG_FIELD_SIZE(R_13), R_13, 0, 0 },
        {20, REG_FIELD_SIZE(R_14), R_14, 0, 0 },
        {21, REG_FIELD_SIZE(R_15), R_15, 0, 0 },
        {22, REG_FIELD_SIZE(R_16), R_16, 0, 0 },
        {23, REG_FIELD_SIZE(R_17), R_17, 0, 0 },
        {24, REG_FIELD_SIZE(R_18), R_18, 0, 0 },
        {25, REG_FIELD_SIZE(R_19), R_19, 0, 0 },
        {26, REG_FIELD_SIZE(R_1A), R_1A, 0, 0 },
        {27, REG_FIELD_SIZE(R_1B), R_1B, 0, 0 },
        {28, REG_FIELD_SIZE(R_1C), R_1C, 0, 0 },
        {29, REG_FIELD_SIZE(R_1D), R_1D, 0, 0 },
        {30, REG_FIELD_SIZE(R_1E), R_1E, 0, 0 },
        {31, REG_FIELD_SIZE(R_1F), R_1F, 0, 0 },
} ;

static REG_All_Registers_t	PTP_PORT_All_Registers[] = {
	{ /* 0 */ REG_REGISTER_SIZE(PTP_PORT_REGS),   PTP_PORT_REGS	},	// port specific registers
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_PTP_PORT  CMDT PTP Port processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_PTP_PORT
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PTP Port register access
 * @details     EsuPtpPort::EsuPtpPort(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and write PTP Port registers.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuPtpPort::EsuPtpPort(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	port_num = 0 ;

	// ioc_data.offs = 0x10000 ;
	ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
}

CliArgs_t * EsuPtpPort :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuPtpPort :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-port", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		port_num = StringToDecHexInt(argv[pos]) ;
		if (port_num < 1 || port_num > 11) {
			cout << "Error: Invalid port number " << port_num << ". Valid range is 1-11." << endl ;
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
	}
	else {
		cout << "Error: Port number is required. Use -port <1-11>" << endl ;
		err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-tsformat", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_val = StringToDecHexInt(argv[pos]) ;    // TS Format value (0 or 1)
			if (reg_val > 1) {
				cout << "Error: Invalid TS format " << reg_val << ". Valid values are 0 (global timer) or 1 (ToD)." << endl ;
				err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
				return (err) ;
			}
			operation = 10 ;	// Set TS Format operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-gettsformat", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 11 ;	// Get TS Format operation
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;	// List one or all sets
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 2 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-wr", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK)
	{
		if (num == 2)
		{
			reg_off = StringToDecHexInt(argv[pos]) ;
			reg_val = StringToDecHexInt(argv[pos+1]) ;
			operation = 1 ;	// Write register operation
		}
		else
		{
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
	}


	err = ScanArgList(argc, argv, "-rd", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK)
	{
		if (num == 1)
		{
			reg_off = StringToDecHexInt(argv[pos]) ;
			operation = 0 ;	// Read register operation
		}
	}
	else
	{
		err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
		return (err) ;
	}

	return (err) ;
}

int EsuPtpPort::ExecuteCmd(uint32_t rw, uint32_t port, uint32_t reg)
{
	uint32_t cmd ;	// G2 AVB/TSN command register: command

	// cmd := Bit 15 + rw-cmd + AvbPort + AvbBlock=0 + AVB register
	//
	cmd = (1 << 15) | ((rw & 3) << 13) | (port << 8) | (0 << 5) | (reg & 0x1F) ;
	err = Esu::WriteIO(0x1C, 0x16, cmd) ;
	if (err == 0) {
		err = Esu::CmdDone(0x1C, 0x16, 15, 0) ;
	}
	return (err) ;
}

int EsuPtpPort::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	err = Esu::WriteIO(0x1C, 0x17, data) ;	// Write data
	if (err == 0) {
		err = ExecuteCmd(3, dev, reg) ;
	}
	if (verbose) {
		cout << "WritePtpPort: port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << data << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuPtpPort::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	err = ExecuteCmd(0, dev, reg) ;
	if (err == 0) {
		err = Esu::ReadIO(0x1C, 0x17, data) ;	// Read data
	}
	if (verbose) {
		cout << "ReadPtpPort : port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << *data << " err:" << err << endl ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuPtpPort::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuPtpPort::postAction(void)
{
	return (err) ;
}

int EsuPtpPort::DoListPtpPort()
{
	err = PrintRegisterList(port_num, PTP_PORT_All_Registers, sizeof(PTP_PORT_All_Registers)/sizeof(REG_All_Registers_t));
	return (err) ;
}

int EsuPtpPort::Action(void)
{
	ioc_data.error = -1 ;

	if (err == 0 && SetDevice(if_name) == true) {
		if (operation == 11)
		{		// get TS format operation
			uint32_t format = 0 ;
			err = GetTSFormat(port_num, &format) ;
			if (err == 0) {
				cout << "PTP Port[" << port_num << "] TS Format: " << format ;
				cout << " (" << (format ? "32-bit ToD" : "32-bit global timer") << ")" << endl ;
			}
		}
		else if (operation == 10)
		{		// set TS format operation
			err = SetTSFormat(port_num, reg_val) ;
			if (err == 0) {
				cout << "PTP Port[" << port_num << "] TS Format set to: " << reg_val ;
				cout << " (" << (reg_val ? "32-bit ToD" : "32-bit global timer") << ")" << endl ;
			}
		}
		else if (operation == 2)
		{		// list all registers
			err = DoListPtpPort() ;
		}
		else if (operation == 1)
		{		// write operation
			err = WriteIO(port_num, reg_off, reg_val) ;
			if (err == 0)
			{
				cout << "PTP_PORT[" << port_num << "] offset:0x" << hex << setw(8) << setfill('0')
					 << reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else if (operation == 0)
		{	// read operation
			err = ReadIO(port_num, reg_off, &reg_val) ;
			if (err == 0)
			{
				cout << "PTP_PORT[" << port_num << "] offset:0x" << hex << setw(8) << setfill('0')
						<< reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			cout << "non sense operation" << endl ;
		}
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuPtpPort::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuPtpPort::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (	strcmp(argv[1], "PTP_PORT") == 0 || strcmp(argv[1], "ptp_port") == 0 ) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/* ========================================================================================
 * PTP Port specific operation functions
 * ======================================================================================== */

/* *********************************************************************************//**
 * @brief       Set PTP Port Timestamp Format
 * @details     int EsuPtpPort::SetTSFormat(uint32_t port, uint32_t format)
 * @details     Sets the timestamp format for PTP port (register 0x00, bit 4)
 * @param [in]  port: Port number (1-11)
 * @param [in]  format: Timestamp format (0=32-bit global timer, 1=32-bit ToD)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::SetTSFormat(uint32_t port, uint32_t format)
{
	uint32_t configReg ;

	if (port < 1 || port > 11) {
		if (verbose) {
			cout << "Error: Invalid port number " << port << ". Valid range is 1-11." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (format > 1) {
		if (verbose) {
			cout << "Error: Invalid TS format " << format << ". Valid values: 0=global timer, 1=ToD." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (verbose) {
		cout << "Setting PTP Port " << port << " TS Format to: " << format ;
		cout << " (" << (format ? "32-bit ToD" : "32-bit global timer") << ")" << endl ;
	}

	// Read current register 0x00 value
	err = ReadIO(port, 0x00, &configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP Port config register 0x00: " << err << endl ;
		}
		return err ;
	}

	// Update TSFormat bit (bit 4) while preserving other bits
	configReg = (configReg & ~(1 << 4)) | ((format & 1) << 4) ;

	// Write back the updated register
	err = WriteIO(port, 0x00, configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error writing PTP Port config register 0x00: " << err << endl ;
		}
		return err ;
	}

	if (verbose) {
		cout << "Successfully set PTP Port " << port << " TS Format to " << format << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Get PTP Port Timestamp Format
 * @details     int EsuPtpPort::GetTSFormat(uint32_t port, uint32_t *format)
 * @details     Gets the current timestamp format for PTP port (register 0x00, bit 4)
 * @param [in]  port: Port number (1-11)
 * @param [out] format: Pointer to store timestamp format (0=32-bit global timer, 1=32-bit ToD)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::GetTSFormat(uint32_t port, uint32_t *format)
{
	uint32_t configReg ;

	if (port < 1 || port > 11) {
		if (verbose) {
			cout << "Error: Invalid port number " << port << ". Valid range is 1-11." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Read register 0x00
	err = ReadIO(port, 0x00, &configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP Port config register 0x00: " << err << endl ;
		}
		return err ;
	}

	// Extract TSFormat bit (bit 4)
	*format = (configReg >> 4) & 1 ;

	if (verbose) {
		cout << "PTP Port " << port << " TS Format: " << *format ;
		cout << " (" << (*format ? "32-bit ToD" : "32-bit global timer") << ")" << endl ;
		cout << "Register 0x00 value: 0x" << hex << configReg << endl ;
	}

	return 0 ;
}

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
