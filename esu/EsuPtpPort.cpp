/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"
#include <ctime>
#include <iomanip>
#include <unistd.h>  // for usleep

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1,  1 },	// Interface name
        { "-port",	1,  1 },	// Port number (required)
        { "-list",	0,  0 },	// List PTP Port registers
        { "-rd",	0,  1 },	// Default: read operation
        { "-wr",	0,  2 },	// write operation
        { "-arrival",	0,  1 },	// Get arrival timestamp: timeIndex
        { "-departure",	0,  0 },	// Get departure timestamp
        { "-config",	0,  4 },	// Configure port: transSpec messageTypes arrivalMode ledControl
        { "-status",	0,  0 },	// Get port status
        { "-delay",	0,  3 },	// Set path delay: ingressMean ingressAsymm egressAsymm
        { "-readcfg",	0,  1 },	// Read PTP Port Config register
        { "-writecfg",	0,  2 },	// Write PTP Port Config register
        { "-v",		0,  0 },	// verbose option
} ;

// PTP Port register definitions based on Figure 16: PTP Port Register Bit Map
// Global 2 Offset 0x16 & 0x17 w/AVBBlock = 0x0 & AVBPort = 11:1
static PTP_PORT_Field_t     R_00[] = {
	{ 15, 14,        "TransSpec",		0, },
	{ 13, 13,        "DT_DisableTranSpecCheck",	0, },
	{ 12, 12,        "MM_MirrorMode",	0, },
	{ 11, 11,        "PT_OneStepSync",	0, },
	{ 10, 10,        "OS_OneStepSync",	0, },
	{  9,  9,        "OE_OneStepEgress",	0, },
	{  8,  8,        "CI_OneStepIngress",	0, },
	{  7,  7,        "MM_MirrorMode2",	0, },
	{  6,  6,        "TF_TimeStampFormat",	0, },
	{  5,  5,        "PC_PTPClockCtrl",	0, },
	{  4,  4,        "DO_DisableOverwrite",	0, },
	{  3,  3,        "DP_DisablePTP",	0, },
	{  2,  2,        "Reserved",		0, },
	{  1,  1,        "Reserved",		0, },
	{  0,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_01[] = {
	{ 15, 15,        "RES_Reserved",	0, },
	{ 14,  8,        "IPJump",		0, },
	{  7,  1,        "Reserved",		0, },
	{  0,  0,        "ETJump",		0, },
} ;

static PTP_PORT_Field_t     R_02[] = {
	{ 15, 15,        "FA_FrameAccelerated",	0, },
	{ 14, 14,        "HA_HardwareAccelerated",	0, },
	{ 13, 13,        "KS_KeepFrameSync",	0, },
	{ 12, 12,        "RSVD_Reserved",	0, },
	{ 11, 11,        "EH_ExternalHardwareAccel",	0, },
	{ 10, 10,        "DE_PTPDepartureIntEnable",	0, },
	{  9,  9,        "AE_PTPArrivalIntEnable",	0, },
	{  8,  0,        "ArrivalTSMode",	0, },
} ;

static PTP_PORT_Field_t     R_03[] = {
	{ 15,  8,        "ArrivalLEDControl",	0, },
	{  7,  0,        "DepartureLEDControl",	0, },
} ;

static PTP_PORT_Field_t     R_04[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_05[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_06[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_07[] = {
	{ 15, 15,        "U_Update",		0, },
	{ 14,  8,        "Pointer",		0, },
	{  7,  0,        "Data",		0, },
} ;

static PTP_PORT_Field_t     R_08[] = {
	{ 15,  8,        "Reserved",		0, },
	{  7,  7,        "AT0_PTPArrival0IntStatus",	0, },
	{  6,  6,        "V0_PTPArrival0TimeValid",	0, },
	{  5,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_09[] = {
	{ 15,  0,        "PTPArrival0Time_15_0",	0, },
} ;

static PTP_PORT_Field_t     R_0A[] = {
	{ 15,  0,        "PTPArrival0Time_31_16",	0, },
} ;

static PTP_PORT_Field_t     R_0B[] = {
	{ 15,  0,        "PTPArrival0SequenceID",	0, },
} ;

static PTP_PORT_Field_t     R_0C[] = {
	{ 15,  8,        "Reserved",		0, },
	{  7,  7,        "AT1_PTPArrival1IntStatus",	0, },
	{  6,  6,        "V1_PTPArrival1TimeValid",	0, },
	{  5,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_0D[] = {
	{ 15,  0,        "PTPArrival1Time_15_0",	0, },
} ;

static PTP_PORT_Field_t     R_0E[] = {
	{ 15,  0,        "PTPArrival1Time_31_16",	0, },
} ;

static PTP_PORT_Field_t     R_0F[] = {
	{ 15,  0,        "PTPArrival1SequenceID",	0, },
} ;

static PTP_PORT_Field_t     R_10[] = {
	{ 15,  8,        "Reserved",		0, },
	{  7,  7,        "DT_PTPDepartureIntStatus",	0, },
	{  6,  6,        "TV_PTPDepartureTimeValid",	0, },
	{  5,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_11[] = {
	{ 15,  0,        "PTPDepartureTime_15_0",	0, },
} ;

static PTP_PORT_Field_t     R_12[] = {
	{ 15,  0,        "PTPDepartureTime_31_16",	0, },
} ;

static PTP_PORT_Field_t     R_13[] = {
	{ 15,  0,        "PTPDepartureSequenceID",	0, },
} ;

static PTP_PORT_Field_t     R_14[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_15[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_16[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_17[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_18[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_19[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_1A[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_1B[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Field_t     R_1C[] = {
	{ 15,  0,        "IngressMeanPathDelay",	0, },
} ;

static PTP_PORT_Field_t     R_1D[] = {
	{ 15,  0,        "IngressPathDelayAsymmetry",	0, },
} ;

static PTP_PORT_Field_t     R_1E[] = {
	{ 15,  0,        "EgressPathDelayAsymmetry",	0, },
} ;

static PTP_PORT_Field_t     R_1F[] = {
	{ 15,  0,        "Reserved",		0, },
} ;

static PTP_PORT_Register_t  PTP_PORT_REGS[] = {
        { 0, REG_FIELD_SIZE(R_00), R_00, 0, 0 },
        { 1, REG_FIELD_SIZE(R_01), R_01, 0, 0 },
        { 2, REG_FIELD_SIZE(R_02), R_02, 0, 0 },
        { 3, REG_FIELD_SIZE(R_03), R_03, 0, 0 },
        { 4, REG_FIELD_SIZE(R_04), R_04, 0, 0 },
        { 5, REG_FIELD_SIZE(R_05), R_05, 0, 0 },
        { 6, REG_FIELD_SIZE(R_06), R_06, 0, 0 },
        { 7, REG_FIELD_SIZE(R_07), R_07, 0, 0 },
        { 8, REG_FIELD_SIZE(R_08), R_08, 0, 0 },
        { 9, REG_FIELD_SIZE(R_09), R_09, 0, 0 },
        {10, REG_FIELD_SIZE(R_0A), R_0A, 0, 0 },
        {11, REG_FIELD_SIZE(R_0B), R_0B, 0, 0 },
        {12, REG_FIELD_SIZE(R_0C), R_0C, 0, 0 },
        {13, REG_FIELD_SIZE(R_0D), R_0D, 0, 0 },
        {14, REG_FIELD_SIZE(R_0E), R_0E, 0, 0 },
        {15, REG_FIELD_SIZE(R_0F), R_0F, 0, 0 },
        {16, REG_FIELD_SIZE(R_10), R_10, 0, 0 },
        {17, REG_FIELD_SIZE(R_11), R_11, 0, 0 },
        {18, REG_FIELD_SIZE(R_12), R_12, 0, 0 },
        {19, REG_FIELD_SIZE(R_13), R_13, 0, 0 },
        {20, REG_FIELD_SIZE(R_14), R_14, 0, 0 },
        {21, REG_FIELD_SIZE(R_15), R_15, 0, 0 },
        {22, REG_FIELD_SIZE(R_16), R_16, 0, 0 },
        {23, REG_FIELD_SIZE(R_17), R_17, 0, 0 },
        {24, REG_FIELD_SIZE(R_18), R_18, 0, 0 },
        {25, REG_FIELD_SIZE(R_19), R_19, 0, 0 },
        {26, REG_FIELD_SIZE(R_1A), R_1A, 0, 0 },
        {27, REG_FIELD_SIZE(R_1B), R_1B, 0, 0 },
        {28, REG_FIELD_SIZE(R_1C), R_1C, 0, 0 },
        {29, REG_FIELD_SIZE(R_1D), R_1D, 0, 0 },
        {30, REG_FIELD_SIZE(R_1E), R_1E, 0, 0 },
        {31, REG_FIELD_SIZE(R_1F), R_1F, 0, 0 },
} ;

static REG_All_Registers_t	PTP_PORT_All_Registers[] = {
	{ /* 0 */ REG_REGISTER_SIZE(PTP_PORT_REGS),   PTP_PORT_REGS	},	// port specific registers
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_PTP_PORT  CMDT PTP Port processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_PTP_PORT
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PTP Port register access
 * @details     EsuPtpPort::EsuPtpPort(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and write PTP Port registers.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuPtpPort::EsuPtpPort(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	port_num = 0 ;

	// ioc_data.offs = 0x10000 ;
	ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
}

CliArgs_t * EsuPtpPort :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuPtpPort :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-port", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		port_num = StringToDecHexInt(argv[pos]) ;
		if (port_num < 1 || port_num > 11) {
			cout << "Error: Invalid port number " << port_num << ". Valid range is 1-11." << endl ;
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
	}
	else {
		cout << "Error: Port number is required. Use -port <1-11>" << endl ;
		err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-delay", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 3) {
			// -delay ingressMean ingressAsymm egressAsymm
			operation = 9 ;	// Set path delay operation
			reg_off = StringToDecHexInt(argv[pos]) ;    // IngressMean
			reg_val = StringToDecHexInt(argv[pos + 1]) ; // IngressAsymm
			reg_val2 = StringToDecHexInt(argv[pos + 2]) ; // EgressAsymm
		}
		else {
			cout << "Error: Invalid number of arguments for -delay. Expected 3, got " << num << endl ;
			cout << "Usage: -delay <ingressMean> <ingressAsymm> <egressAsymm>" << endl ;
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-config", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 4) {
			// -config transSpec messageTypes arrivalMode ledControl
			operation = 8 ;	// Configure port operation
			reg_off = StringToDecHexInt(argv[pos]) ;    // TransSpec
			reg_val = StringToDecHexInt(argv[pos + 1]) ; // MessageTypes
			reg_val2 = StringToDecHexInt(argv[pos + 2]) ; // ArrivalMode
			reg_val3 = StringToDecHexInt(argv[pos + 3]) ; // LEDControl
		}
		else {
			cout << "Error: Invalid number of arguments for -config. Expected 4, got " << num << endl ;
			cout << "Usage: -config <transSpec> <messageTypes> <arrivalMode> <ledControl>" << endl ;
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-readcfg", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;  // Config pointer
			operation = 7 ;	// Read PTP Port Config operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-writecfg", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;    // Config pointer
			reg_val = StringToDecHexInt(argv[pos+1]) ;  // Data value
			operation = 6 ;	// Write PTP Port Config operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-status", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 5 ;	// Get port status operation
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-departure", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 4 ;	// Get departure timestamp operation
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-arrival", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			reg_off = StringToDecHexInt(argv[pos]) ;    // TimeIndex (0 or 1)
			if (reg_off > 1) {
				cout << "Error: Invalid time index " << reg_off << ". Valid values are 0 or 1." << endl ;
				err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
				return (err) ;
			}
			operation = 3 ;	// Get arrival timestamp operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;	// List one or all sets
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 2 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-wr", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;
			reg_val = StringToDecHexInt(argv[pos+1]) ;
			operation = 1 ;	// Write register operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			return (err) ;
		}
	}
	else {

		err = ScanArgList(argc, argv, "-rd", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			if (num == 1) {
				reg_off = StringToDecHexInt(argv[pos]) ;
				operation = 0 ;	// Read register operation
			}
		}
		else {
			err = ScanArgList(argc, argv, "-A", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num > 0) {
                        	err = ReadActionArgumentList(&argv[pos], num,
                                	PTP_PORT_All_Registers, sizeof(PTP_PORT_All_Registers)/sizeof(PTP_PORT_All_Registers_t)) ;
				operation = 10 ;	// Set register bit fields
                	}
                	else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
                        	err = IOCTL_CMD_STATUS_OK ;
                	}
		}
	}

	return (err) ;
}

int EsuPtpPort::ExecuteCmd(uint32_t rw, uint32_t port, uint32_t reg)
{
	uint32_t cmd ;	// G2 AVB/TSN command register: command

	// cmd := Bit 15 + rw-cmd + AvbPort + AvbBlock=0 + AVB register
	//
	cmd = (1 << 15) | ((rw & 3) << 13) | (port << 8) | (0 << 5) | (reg & 0x1F) ;
	err = Esu::WriteIO(0x1C, 0x16, cmd) ;
	if (err == 0) {
		err = Esu::CmdDone(0x1C, 0x16, 15, 0) ;
	}
	return (err) ;
}

int EsuPtpPort::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	err = Esu::WriteIO(0x1C, 0x17, data) ;	// Write data
	if (err == 0) {
		err = ExecuteCmd(3, dev, reg) ;
	}
	if (verbose) {
		cout << "WritePtpPort: port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << data << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuPtpPort::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	err = ExecuteCmd(0, dev, reg) ;
	if (err == 0) {
		err = Esu::ReadIO(0x1C, 0x17, data) ;	// Read data
	}
	if (verbose) {
		cout << "ReadPtpPort : port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << *data << " err:" << err << endl ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuPtpPort::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuPtpPort::postAction(void)
{
	return (err) ;
}

int EsuPtpPort::DoListPtpPort()
{
	err = PrintRegisterList(port_num, PTP_PORT_All_Registers, sizeof(PTP_PORT_All_Registers)/sizeof(REG_All_Registers_t));
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Read PTP Port Config register using pointer mechanism
 * @details     int EsuPtpPort::ReadPtpPortConfig(uint32_t port, uint32_t pointer, uint32_t *data)
 * @details     Reads from PTP Port Config register at offset 0x07 using pointer mechanism
 * @param [in]  port: Port number (1-11)
 * @param [in]  pointer: Pointer to desired config register
 * @param [out] data: Pointer to store the read data
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::ReadPtpPortConfig(uint32_t port, uint32_t pointer, uint32_t *data)
{
	uint32_t configReg = 0 ;

	// Step 1: Write pointer with Update=0 to select the config register
	configReg = (pointer << 8) ;  // Pointer bits 14:8, Update bit 15 = 0

	err = WriteIO(port, 0x07, configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error setting PTP Port Config pointer: " << err << endl ;
		}
		return err ;
	}

	// Step 2: Read back the register to get the data
	err = ReadIO(port, 0x07, &configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP Port Config data: " << err << endl ;
		}
		return err ;
	}

	// Extract data from bits 7:0
	*data = configReg & 0xFF ;

	if (verbose) {
		cout << "Read PTP Port[" << port << "] Config[0x" << hex << pointer << "] = 0x" << hex << *data << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Write PTP Port Config register using pointer mechanism
 * @details     int EsuPtpPort::WritePtpPortConfig(uint32_t port, uint32_t pointer, uint32_t data)
 * @details     Writes to PTP Port Config register at offset 0x07 using pointer mechanism
 * @param [in]  port: Port number (1-11)
 * @param [in]  pointer: Pointer to desired config register
 * @param [in]  data: Data to write (8-bit value)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::WritePtpPortConfig(uint32_t port, uint32_t pointer, uint32_t data)
{
	uint32_t configReg = 0 ;

	if (data > 0xFF) {
		if (verbose) {
			cout << "Error: Invalid data " << data << ". Valid range is 0x00-0xFF." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Write pointer with Update=1 and data to update the config register
	configReg = (1 << 15) | (pointer << 8) | (data & 0xFF) ;  // Update=1, Pointer, Data

	err = WriteIO(port, 0x07, configReg) ;
	if (err != 0) {
		if (verbose) {
			cout << "Error writing PTP Port Config: " << err << endl ;
		}
		return err ;
	}

	if (verbose) {
		cout << "Write PTP Port[" << port << "] Config[0x" << hex << pointer << "] = 0x" << hex << data << endl ;
	}

	return 0 ;
}

int EsuPtpPort::Action(void)
{
	ioc_data.error = -1 ;

	if (err == 0 && SetDevice(if_name) == true) {
		if (operation == 9)
		{		// set path delay operation
			err = SetPtpPathDelay(port_num, reg_off, reg_val, reg_val2) ;
		}
		else if (operation == 8)
		{		// configure port operation
			err = ConfigurePtpPort(port_num, reg_off, reg_val, reg_val2, reg_val3) ;
		}
		else if (operation == 7)
		{		// read PTP port config operation
			err = ReadPtpPortConfig(port_num, reg_off, &reg_val) ;
			if (err == 0) {
				cout << "PTP Port[" << port_num << "] Config[0x" << hex << reg_off << "] = 0x" << hex << reg_val << endl ;
			}
		}
		else if (operation == 6)
		{		// write PTP port config operation
			err = WritePtpPortConfig(port_num, reg_off, reg_val) ;
			if (err == 0) {
				cout << "PTP Port[" << port_num << "] Config[0x" << hex << reg_off << "] = 0x" << hex << reg_val << endl ;
			}
		}
		else if (operation == 5)
		{		// get port status operation
			uint32_t arrivalStatus = 0, departureStatus = 0 ;
			err = GetPtpPortStatus(port_num, &arrivalStatus, &departureStatus) ;
		}
		else if (operation == 4)
		{		// get departure timestamp operation
			uint64_t timestamp = 0 ;
			uint32_t sequenceId = 0 ;
			err = GetPtpDepartureTime(port_num, &timestamp, &sequenceId) ;
		}
		else if (operation == 3)
		{		// get arrival timestamp operation
			uint64_t timestamp = 0 ;
			uint32_t sequenceId = 0 ;
			err = GetPtpArrivalTime(port_num, reg_off, &timestamp, &sequenceId) ;
		}
		else if (operation == 2)
		{		// list all registers
			err = DoListPtpPort() ;
		}
		else if (operation == 1)
		{		// write operation
			err = WriteIO(port_num, reg_off, reg_val) ;
			if (err == 0)
			{
				cout << "PTP_PORT[" << port_num << "] offset:0x" << hex << setw(8) << setfill('0')
					 << reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else if (operation == 0)
		{	// read operation
			err = ReadIO(port_num, reg_off, &reg_val) ;
			if (err == 0)
			{
				cout << "PTP_PORT[" << port_num << "] offset:0x" << hex << setw(8) << setfill('0')
						<< reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;
			cout << "non sense operation" << endl ;
		}
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuPtpPort::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuPtpPort::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (	strcmp(argv[1], "PTP_PORT") == 0 || strcmp(argv[1], "ptp_port") == 0 ) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/* ========================================================================================
 * PTP Port specific operation functions
 * ======================================================================================== */

/* *********************************************************************************//**
 * @brief       Wait for PTP Port to be ready
 * @details     int EsuPtpPort::WaitPortReady(uint32_t port)
 * @details     Waits for port operations to complete
 * @param [in]  port: Port number (1-11)
 * @return      error code: 0 on success, non-zero on timeout
 * *************************************************************************************/
int EsuPtpPort::WaitPortReady(uint32_t port)
{
	// For PTP Port operations, we typically don't need to wait like in Global operations
	// This is a placeholder for future use if needed
	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Get PTP Arrival Time
 * @details     int EsuPtpPort::GetPtpArrivalTime(uint32_t port, uint32_t timeIndex, uint64_t *timestamp, uint32_t *sequenceId)
 * @details     Reads PTP arrival timestamp and sequence ID from specified time index
 * @param [in]  port: Port number (1-11)
 * @param [in]  timeIndex: Time index (0 or 1)
 * @param [out] timestamp: Pointer to store 32-bit timestamp
 * @param [out] sequenceId: Pointer to store sequence ID
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::GetPtpArrivalTime(uint32_t port, uint32_t timeIndex, uint64_t *timestamp, uint32_t *sequenceId)
{
	uint32_t statusReg, timeLo, timeHi, seqId ;
	uint32_t statusOffset, timeLoOffset, timeHiOffset, seqIdOffset ;

	if (port < 1 || port > 11) {
		if (verbose) {
			cout << "Error: Invalid port number " << port << ". Valid range is 1-11." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (timeIndex > 1) {
		if (verbose) {
			cout << "Error: Invalid time index " << timeIndex << ". Valid values are 0 or 1." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Determine register offsets based on time index
	if (timeIndex == 0) {
		statusOffset = 0x08 ;  // PTP Arrival 0 Status
		timeLoOffset = 0x09 ;  // PTP Arrival 0 Time [15:0]
		timeHiOffset = 0x0A ;  // PTP Arrival 0 Time [31:16]
		seqIdOffset = 0x0B ;   // PTP Arrival 0 Sequence ID
	} else {
		statusOffset = 0x0C ;  // PTP Arrival 1 Status
		timeLoOffset = 0x0D ;  // PTP Arrival 1 Time [15:0]
		timeHiOffset = 0x0E ;  // PTP Arrival 1 Time [31:16]
		seqIdOffset = 0x0F ;   // PTP Arrival 1 Sequence ID
	}

	// Read status register first
	err = ReadIO(port, statusOffset, &statusReg) ;
	if (err != 0) return err ;

	// Check if time is valid (bit 6)
	if ((statusReg & 0x40) == 0) {
		if (verbose) {
			cout << "Warning: PTP Arrival " << timeIndex << " Time is not valid for port " << port << endl ;
		}
		*timestamp = 0 ;
		*sequenceId = 0 ;
		return 1 ;  // Not an error, but indicates no valid data
	}

	// Read timestamp
	err = ReadIO(port, timeLoOffset, &timeLo) ;
	if (err == 0) err = ReadIO(port, timeHiOffset, &timeHi) ;
	if (err == 0) err = ReadIO(port, seqIdOffset, &seqId) ;

	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP arrival time registers: " << err << endl ;
		}
		return err ;
	}

	// Reconstruct timestamp
	*timestamp = ((uint64_t)timeHi << 16) | timeLo ;
	*sequenceId = seqId ;

	if (verbose) {
		cout << "PTP Port[" << port << "] Arrival " << timeIndex << " Time: 0x" << hex << *timestamp
			 << " (" << dec << *timestamp << "), Sequence ID: " << *sequenceId << endl ;
		cout << "Status: AT" << timeIndex << "=" << ((statusReg & 0x80) ? "1" : "0")
			 << ", V" << timeIndex << "=" << ((statusReg & 0x40) ? "1" : "0") << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Get PTP Departure Time
 * @details     int EsuPtpPort::GetPtpDepartureTime(uint32_t port, uint64_t *timestamp, uint32_t *sequenceId)
 * @details     Reads PTP departure timestamp and sequence ID
 * @param [in]  port: Port number (1-11)
 * @param [out] timestamp: Pointer to store 32-bit timestamp
 * @param [out] sequenceId: Pointer to store sequence ID
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::GetPtpDepartureTime(uint32_t port, uint64_t *timestamp, uint32_t *sequenceId)
{
	uint32_t statusReg, timeLo, timeHi, seqId ;

	if (port < 1 || port > 11) {
		if (verbose) {
			cout << "Error: Invalid port number " << port << ". Valid range is 1-11." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Read status register first
	err = ReadIO(port, 0x10, &statusReg) ;  // PTP Departure Status
	if (err != 0) return err ;

	// Check if time is valid (bit 6)
	if ((statusReg & 0x40) == 0) {
		if (verbose) {
			cout << "Warning: PTP Departure Time is not valid for port " << port << endl ;
		}
		*timestamp = 0 ;
		*sequenceId = 0 ;
		return 1 ;  // Not an error, but indicates no valid data
	}

	// Read timestamp
	err = ReadIO(port, 0x11, &timeLo) ;   // PTP Departure Time [15:0]
	if (err == 0) err = ReadIO(port, 0x12, &timeHi) ;   // PTP Departure Time [31:16]
	if (err == 0) err = ReadIO(port, 0x13, &seqId) ;    // PTP Departure Sequence ID

	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP departure time registers: " << err << endl ;
		}
		return err ;
	}

	// Reconstruct timestamp
	*timestamp = ((uint64_t)timeHi << 16) | timeLo ;
	*sequenceId = seqId ;

	if (verbose) {
		cout << "PTP Port[" << port << "] Departure Time: 0x" << hex << *timestamp
			 << " (" << dec << *timestamp << "), Sequence ID: " << *sequenceId << endl ;
		cout << "Status: DT=" << ((statusReg & 0x80) ? "1" : "0")
			 << ", TV=" << ((statusReg & 0x40) ? "1" : "0") << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Configure PTP Port
 * @details     int EsuPtpPort::ConfigurePtpPort(uint32_t port, uint32_t transSpec, uint32_t messageTypes, uint32_t arrivalMode, uint32_t ledControl)
 * @details     Configures PTP port settings including transport specification, message types, arrival mode, and LED control
 * @param [in]  port: Port number (1-11)
 * @param [in]  transSpec: Transport specification (bits 15:14 of register 0x00)
 * @param [in]  messageTypes: Message type enables (register 0x02 bits 8:0)
 * @param [in]  arrivalMode: Arrival timestamp mode (register 0x02 bits 8:0)
 * @param [in]  ledControl: LED control settings (register 0x03)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::ConfigurePtpPort(uint32_t port, uint32_t transSpec, uint32_t messageTypes, uint32_t arrivalMode, uint32_t ledControl)
{
	uint32_t configReg ;

	if (port < 1 || port > 11) {
		if (verbose) {
			cout << "Error: Invalid port number " << port << ". Valid range is 1-11." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (verbose) {
		cout << "Configuring PTP Port " << port << ":" << endl ;
		cout << "  TransSpec: 0x" << hex << transSpec << endl ;
		cout << "  MessageTypes: 0x" << hex << messageTypes << endl ;
		cout << "  ArrivalMode: 0x" << hex << arrivalMode << endl ;
		cout << "  LEDControl: 0x" << hex << ledControl << endl ;
	}

	// Configure register 0x00 (TransSpec and other control bits)
	err = ReadIO(port, 0x00, &configReg) ;
	if (err != 0) return err ;

	// Update TransSpec bits (15:14) while preserving other bits
	configReg = (configReg & 0x3FFF) | ((transSpec & 0x3) << 14) ;
	err = WriteIO(port, 0x00, configReg) ;
	if (err != 0) return err ;

	// Configure register 0x02 (Arrival TS Mode and message type enables)
	configReg = (arrivalMode & 0x1FF) | ((messageTypes & 0x7) << 9) ;
	err = WriteIO(port, 0x02, configReg) ;
	if (err != 0) return err ;

	// Configure register 0x03 (LED Control)
	err = WriteIO(port, 0x03, ledControl & 0xFFFF) ;
	if (err != 0) return err ;

	if (verbose) {
		cout << "Successfully configured PTP Port " << port << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Get PTP Port Status
 * @details     int EsuPtpPort::GetPtpPortStatus(uint32_t port, uint32_t *arrivalStatus, uint32_t *departureStatus)
 * @details     Reads PTP port status including arrival and departure interrupt status
 * @param [in]  port: Port number (1-11)
 * @param [out] arrivalStatus: Pointer to store arrival status (combines both arrival 0 and 1 status)
 * @param [out] departureStatus: Pointer to store departure status
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::GetPtpPortStatus(uint32_t port, uint32_t *arrivalStatus, uint32_t *departureStatus)
{
	uint32_t arrival0Status, arrival1Status, depStatus ;

	if (port < 1 || port > 11) {
		if (verbose) {
			cout << "Error: Invalid port number " << port << ". Valid range is 1-11." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	// Read arrival status registers
	err = ReadIO(port, 0x08, &arrival0Status) ;  // PTP Arrival 0 Status
	if (err == 0) err = ReadIO(port, 0x0C, &arrival1Status) ;  // PTP Arrival 1 Status
	if (err == 0) err = ReadIO(port, 0x10, &depStatus) ;       // PTP Departure Status

	if (err != 0) {
		if (verbose) {
			cout << "Error reading PTP port status registers: " << err << endl ;
		}
		return err ;
	}

	// Combine arrival status (arrival0 in lower byte, arrival1 in upper byte)
	*arrivalStatus = (arrival0Status & 0xFF) | ((arrival1Status & 0xFF) << 8) ;
	*departureStatus = depStatus & 0xFF ;

	if (verbose) {
		cout << "PTP Port[" << port << "] Status:" << endl ;
		cout << "  Arrival 0: AT0=" << ((arrival0Status & 0x80) ? "1" : "0")
			 << ", V0=" << ((arrival0Status & 0x40) ? "1" : "0") << endl ;
		cout << "  Arrival 1: AT1=" << ((arrival1Status & 0x80) ? "1" : "0")
			 << ", V1=" << ((arrival1Status & 0x40) ? "1" : "0") << endl ;
		cout << "  Departure: DT=" << ((depStatus & 0x80) ? "1" : "0")
			 << ", TV=" << ((depStatus & 0x40) ? "1" : "0") << endl ;
	}

	return 0 ;
}

/* *********************************************************************************//**
 * @brief       Set PTP Path Delay
 * @details     int EsuPtpPort::SetPtpPathDelay(uint32_t port, uint32_t ingressMean, uint32_t ingressAsymm, uint32_t egressAsymm)
 * @details     Sets PTP path delay compensation values for the port
 * @param [in]  port: Port number (1-11)
 * @param [in]  ingressMean: Ingress mean path delay (register 0x1C)
 * @param [in]  ingressAsymm: Ingress path delay asymmetry (register 0x1D)
 * @param [in]  egressAsymm: Egress path delay asymmetry (register 0x1E)
 * @return      error code: 0 on success, non-zero on error
 * *************************************************************************************/
int EsuPtpPort::SetPtpPathDelay(uint32_t port, uint32_t ingressMean, uint32_t ingressAsymm, uint32_t egressAsymm)
{
	if (port < 1 || port > 11) {
		if (verbose) {
			cout << "Error: Invalid port number " << port << ". Valid range is 1-11." << endl ;
		}
		return IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
	}

	if (verbose) {
		cout << "Setting PTP Path Delay for Port " << port << ":" << endl ;
		cout << "  Ingress Mean Path Delay: 0x" << hex << ingressMean << " (" << dec << ingressMean << ")" << endl ;
		cout << "  Ingress Path Delay Asymmetry: 0x" << hex << ingressAsymm << " (" << dec << ingressAsymm << ")" << endl ;
		cout << "  Egress Path Delay Asymmetry: 0x" << hex << egressAsymm << " (" << dec << egressAsymm << ")" << endl ;
	}

	// Write path delay registers
	err = WriteIO(port, 0x1C, ingressMean & 0xFFFF) ;      // Ingress Mean Path Delay
	if (err == 0) err = WriteIO(port, 0x1D, ingressAsymm & 0xFFFF) ;  // Ingress Path Delay Asymmetry
	if (err == 0) err = WriteIO(port, 0x1E, egressAsymm & 0xFFFF) ;   // Egress Path Delay Asymmetry

	if (err != 0) {
		if (verbose) {
			cout << "Error writing PTP path delay registers: " << err << endl ;
		}
		return err ;
	}

	if (verbose) {
		cout << "Successfully set PTP path delay for Port " << port << endl ;
	}

	return 0 ;
}

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
