/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef ESU_IO_H
#define ESU_IO_H

#include "Socket.h"
#include "Cli.h"
#include "oak_ioc_reg.h"
#include <map>
class Esu : public Cli, public Socket
{
	private:
		bool is030Model;
		void detectHardwareModel();
	public:
		Esu(int argc, char* argv[]);
		uint32_t mapExternalToPhyAddr(uint32_t externalPort) const;
		uint32_t mapPhyAddrToExternal(uint32_t phyAddr) const;
		uint32_t mapExternalToInternal(uint32_t externalPort) const;
		uint32_t mapInternalToExternal(uint32_t internalPort) const;

	protected:
		virtual int	WriteIO(uint32_t dev, uint32_t reg, uint32_t data) ;
		virtual int	ReadIO(uint32_t dev, uint32_t reg, uint32_t *data) ;
		virtual int	CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val, uint32_t to = 100) ;
		virtual int 	ReadRegisterList(uint32_t dev, REG_All_Registers_t *tab) ;
		virtual void 	PrintBitField(uint32_t reg_offs, REG_Field_t *bit_field) ;
		virtual int 	PrintRegisterList(uint32_t dev, REG_All_Registers_t *tab, uint32_t tab_size) ;

		void 		SetRegisterData(REG_Register_t *reg) ;
		void 		SetAllRegisterData(REG_All_Registers_t *tab, uint32_t tab_size) ;
		int 		WriteRegisterList(uint32_t dev, REG_All_Registers_t *tab) ;

		void 		SetActionParameterBits(REG_Field_t *bit_field, uint32_t val) ;
		bool		SetValidActionParameter(char *ptr, REG_All_Registers_t *tab, uint32_t tab_size) ;
		int 		ReadActionArgumentList(char *argv[], int num,
					REG_All_Registers_t *tab, uint32_t tab_size) ;

		int		ioc_cmd ;
		oak_ioc_reg	ioc_data ;
} ;

#endif // ESUATU_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
