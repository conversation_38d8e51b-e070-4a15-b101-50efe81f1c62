/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-dev",	1, 1 },	// Device,Port number
        { "-list",	0, 0 },	// List QBV sets
        { "-te",	0, 1 },	// Table entry number
        { "-gb",	0, 0 },	// Guard band 
        { "-set",	1, 1 },	// Set number
        { "-rd",	0, 1 },	// Default: read operation
        { "-wr",	0, 2 },	// write operation
        { "-v",		0, 0 },	// verbose option
        { "-test",	0, 0 },	// test preemption cpability
        { "-pe",	0, 1 },	// set preemption cpability
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     EsuQbv::EsuQbv(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuQbv::EsuQbv(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	dev_no  = 0x00 ; // Default: port 0
	tab_set = 0 ;
	pointer = 0 ;
	reg_off = 0 ;
	reg_val = 0 ;
	qstate  = 0xFF ;
	operation  = 0 ;
	pre_emption = 0 ;

	// ioc_data.offs = 0x10000 ;
	ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;

	pEsuTai = new EsuTai(argc, argv) ;
}

CliArgs_t * EsuQbv :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuQbv :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-dev", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		dev_no = StringToDecHexInt(argv[pos]) ;
	}

	err = ScanArgList(argc, argv, "-test", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 4 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-pe", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 1) {
			pre_emption = StringToDecHexInt(argv[pos]) ;
		}
		operation = 5 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-set", &pos, &num) ;	// Set: 1 or 2
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		tab_set = StringToDecHexInt(argv[pos]) ;
		if (tab_set == 1 || tab_set == 2) {
			pointer |= ((tab_set-1) << 6) ;
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;	
			return (err) ;
		}
	}

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;	// List one or all sets
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 3 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-te", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num != 1) {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;	
			return (err) ;
		}
		num = StringToDecHexInt(argv[pos]) ;
		pointer |= (num & 0xF) ;
	}
	else {
		err = ScanArgList(argc, argv, "-gb", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			pointer |= 0x20 ;	// Guard band 1
		}
	}

	err = ScanArgList(argc, argv, "-wr", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;
			reg_val = StringToDecHexInt(argv[pos+1]) ;
			operation = 2 ;	// Write register operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;	
			return (err) ;
		}
	}
	else {

		err = ScanArgList(argc, argv, "-rd", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			if (num == 1) {
				reg_off = StringToDecHexInt(argv[pos]) ;
				operation = 1 ;	// Read register operation
			}
		}
		else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
			err = IOCTL_CMD_STATUS_OK ;
		}
	}

	return (err) ;
}

int EsuQbv::ExecuteCmd(uint32_t rw, uint32_t port, uint32_t reg)
{
	uint32_t cmd ;	// G2 AVB/TSN command register: command

	// cmd := Bit 15 + rw-cmd + AvbPort=0x1F + AvbBlock=3 + AVB register
	//
	cmd = (1 << 15) | ((rw & 3) << 13) | (port << 8) | (3 << 5) | (reg & 0x1F) ;
	err = Esu::WriteIO(0x1C, 0x16, cmd) ;
	if (err == 0) {
		err = CmdDone(0x1C, 0x16, 15, 0) ;
	}
	return (err) ;
}

int EsuQbv::WriteIO(uint32_t port, uint32_t reg, uint32_t data)
{
	err = Esu::WriteIO(0x1C, 0x17, data) ;	// Write data
	if (err == 0) {
		err = ExecuteCmd(3, port, reg) ;
	}
	if (verbose) {
		cout << "WriteQbv: port=0x" << hex << port << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << data << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuQbv::ReadIO(uint32_t port, uint32_t reg, uint32_t *data)
{
	err = ExecuteCmd(0, port, reg) ;
	if (err == 0) {
		err = Esu::ReadIO(0x1C, 0x17, data) ;	// Read data
	}
	if (verbose) {
		cout << "ReadQbv : port=0x" << hex << port << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << *data << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuQbv::QbvCmdDone(uint32_t port, uint32_t reg, uint32_t b, uint32_t val)
{
        uint32_t        data ;

        val &= 1 ;
        err = 0 ;
        for (int i=0; i<100 && err==0 ; i++) {
                err = ReadIO(port, reg, &data) ;
                if (err == 0 && ((data >> b) & 1) == val) {
                        return(0) ;
                }
                usleep(100) ;
        }
        if (err == 0) {
                err = IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
        }
        return (err) ;
}


int EsuQbv::WriteQbvPortTableControl(uint32_t port, uint32_t ptr, uint32_t qstate)
{
	uint32_t cmd ;

	// cmd := 0x8000 + prt + AvbBlock=3 + AVB register
	cmd = (1 << 15) | ((ptr & 0x7F) << 8) | (qstate & 0xff) ;
	err = WriteIO(port, 2, cmd) ;
	if (err == 0) {
		err = QbvCmdDone(port, 2, 15, 0) ;
	}
	return (err) ;
}

int EsuQbv::ReadQbvPortTableControl(uint32_t port, uint32_t ptr)
{
	uint32_t cmd ;

	// cmd := 0x0000 + prt + AvbBlock=3 + AVB register
	cmd = (0 << 15) | ((ptr & 0x7F) << 8) ;
	err = WriteIO(port, 2, cmd) ;
	if (err == 0) {
		err = QbvCmdDone(port, 2, 15, 0) ;
	}
	return (err) ;
}


/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuQbv::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuQbv::postAction(void)
{ 
}

int EsuQbv::DoListTableEntry(uint32_t ptr, uint32_t set)
{
	err = ReadQbvPortTableControl(dev_no, ptr) ;

	if (err == 0) {
		uint32_t data ;

		if (ReadIO(dev_no, 2, &data) == 0) {
			cout << "Table entry: " << dec << setw(2) << setfill('0') << (ptr & 15) << "\t\t" ;
			cout << "QueueState       : " <<  hex << setw(4) << setfill('0') << (data & 0xff) << "        " ;
		}
		if (ReadIO(dev_no, 3, &data) == 0) {
			cout << "GuardBand  : " <<  dec << ((data >> 15) & 1) << "        " ;
			cout << "WindowTime : " <<  dec << setw(9) << setfill(' ') << (data & 0x7fff)  << " ns     " ;

			if (set == 2) {
				if (ReadIO(dev_no, 4, &data) == 0) {
					cout << "AllowBytes : " <<  dec << setw(8) << setfill(' ') << (data & 0xffff) << endl ;
				}
			}
			else {
				cout << endl ;
			}
		}
	}
}

int EsuQbv::DoListGuardBand(uint32_t ptr)
{
err = ReadQbvPortTableControl(dev_no, ptr) ;
if (err == 0) {
	uint32_t data ;
	if (ReadIO(dev_no, 3, &data) == 0) {
		cout << "\nGuardBand " << dec << (((ptr >> 6) & 1)+1) << ": " << (data & 0x3fff) << " bytes" ;
	}
	if (ReadIO(dev_no, 2, &data) == 0) {
		cout << "\tQueue state: 0x" << hex << (data & 0xff) << endl << endl; 
	}
}
}

int EsuQbv::DoListGlobal()
{
	uint32_t val, data ;


	if (ReadIO(0x1F, 0, &data) == 0) {
		cout << "\nGlobal settings" << endl ;
		cout << "ActiveTimeSet       (RO): " <<  dec << setw(8) << setfill(' ') << ((data >> 4) & 3) << "\t" ;
		cout << "SwitchOverMode          : " <<  dec << setw(8) << setfill(' ') << ((data >> 2) & 3) << "\t" ;
		cout << "TimeSet             (SC): " <<  dec << setw(8) << setfill(' ') << ((data >> 0) & 3) << endl ;
		if (tab_set == 0) {
			tab_set = (data >> 4) & 3 ;
		}
	}
	if (ReadIO(0x1F, 1, &data) == 0) {
		cout << "KeepFiltering           : " <<  dec << setw(8) << setfill(' ') << ((data >> 4) & 1) << "\t" ;
		cout << "ByteScale               : " <<  dec << setw(8) << setfill(' ') << ((data >> 2) & 3) << "\t" ;
		cout << "TimeScale               : " <<  dec << setw(8) << setfill(' ') << ((data >> 0) & 3) << endl ;
	}
	if (ReadIO(0x1F, 2, &data) == 0) {
		val = data & 0xFFFF ;
	}
	if (ReadIO(0x1F, 7, &data) == 0) {
		cout << "PortQBVAlarms       (RO):  0x" <<  hex << setw(8) << setfill('0') << (data & 0x7ff) << "\t" ;
	}
	if (ReadIO(0x1F, 8, &data) == 0) {
		cout << "PortSelect              :  0x" <<  hex << setw(8) << setfill('0') << ((data >> 8) & 0xf) << endl ;
	}
	if (ReadIO(0x1F, 3, &data) == 0) {
		val = val | ((data & 0xFFFF) << 16) ;
		cout << "TimeSetSwitchTime       :  0x" <<  hex << setw(8) << setfill('0') << val << " ns" << endl ;
	}
}

int EsuQbv::DoListPort()
{
	uint32_t data = 0 ;


	if (ReadIO(dev_no, 0, &data) == 0) {
		cout << "\nPort settings" << endl ;
		cout << "PortDelay1              : " <<  dec << setw(8) << setfill(' ') << data * 64 << " ns" << "\t" ;
	}
	if (ReadIO(dev_no, 1, &data) == 0) {
		cout << "PortDelay2              : " <<  dec << setw(8) << setfill(' ') << data * 64 << " ns" << endl ;
	}
#if 0
	if (ReadIO(dev_no, 4, &data) == 0) {
		cout << "AllowedBytes            : " <<  dec << setw(8) << setfill(' ') << data << endl ;
	}
#endif
	if (ReadIO(dev_no, 7, &data) == 0) {
		cout << "FilteredPriorities  (RO): " <<  dec << setw(8) << setfill(' ') << ((data >> 8) & 0xFF) << "\t" ;
		cout << "PolicedPriorities       :  0x"<<hex << setw(8) << setfill('0') << ((data >> 0) & 0xFF) << endl ;
	}
	if (ReadIO(dev_no, 8, &data) == 0) {
		cout << "GBMode0                 : " <<  dec << setw(8) << setfill(' ') << ((data >>  4) & 0x03) << "\t" ;
		cout << "GBMode1                 : " <<  dec << setw(8) << setfill(' ') << ((data >> 12) & 0x03) << endl ;
		cout << "SetSelect0              : " <<  dec << setw(8) << setfill(' ') << ((data >>  3) & 0x01) << "\t" ;
		cout << "SetSelect1              : " <<  dec << setw(8) << setfill(' ') << ((data >> 11) & 0x01) << endl ;
		cout << "QueueSel0               : " <<  dec << setw(8) << setfill(' ') << ((data >>  0) & 0x07) << "\t" ;
		cout << "QueueSel1               : " <<  dec << setw(8) << setfill(' ') << ((data >>  8) & 0x07) << endl << endl ;
	}
}

int EsuQbv::DoListTable(uint32_t set)
{
	uint32_t data ;
	pointer = ((set-1) << 6) ;

	cout << "Table set  : " << dec << set << endl ;

	for (uint32_t idx = 0 ; idx <= 15; idx++) {
		DoListTableEntry(pointer | idx, set) ;
	}
	DoListGuardBand(pointer | 0x20) ;
}

int EsuQbv::DoList(void)
{
	uint32_t tlo, thi ;

	err = GetPtpGlobalTime(&tlo, &thi) ;

	if (err == 0) {

		cout << "\nQBV SETTINGS FOR PORT   : " << dev_no << "\t\tPTP Time:0x" << hex << setw(4) << setfill('0') << thi << tlo <<  endl ;

		DoListGlobal() ;
		DoListPort() ;

		if (tab_set == 1 || tab_set == 2) {
			DoListTable(tab_set) ;
		}
		else {
			DoListTable(1) ;
			DoListTable(2) ;
		}
	}
	return (err) ;
}

int EsuQbv::GetPtpGlobalTime(uint32_t *tlo, uint32_t *thi)
{
	uint32_t tim_lo, tim_hi ;

	if (pEsuTai != NULL) {
		pEsuTai->SetDevice(if_name) ;
		err = pEsuTai->ReadIO(0x1E,14,tlo) ;
		if (err == 0) {
			err = pEsuTai->ReadIO(0x1E,15,thi) ;
		}
	}
	else {
		err = IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND ;
	}
	return (err) ;
}

int EsuQbv::DoTestPreemption()
{
	uint32_t	data = (1 << 15) ;

	err = Esu::WriteIO(dev_no, 0x15, data) ;	// Preemption control
	if (err == 0) {
		err = CmdDone(dev_no, 0x15, 15, 0, 100*1000) ;	// timeout 100 ms
	}
	if (err == 0) {
		err = Esu::ReadIO(dev_no, 0x15, &data);	// Preemption control
		if (err == 0) {
			cout << "Preemption test completed at port=0x:" << dec << dev_no ;
			if ((data & (1 << 14)) != 0) {
				cout << " supported" ;
			}
			else {
				cout << " not completed" ;
			}
			err = Esu::ReadIO(dev_no, 0x00, &data);	// Preemption control
			if (err == 0) {
				cout << ", duplex: " << ((data >> 10) & 1) << endl ;
			}
			cout << endl ;
		}
	}
	return (err);
}

int EsuQbv::DoEnablePreemption(uint32_t on)
{
	uint32_t data = ((on & 1) << 10) ;
	err = Esu::WriteIO(dev_no, 0x15, data) ;	// RX preemption on/off
	if (err == 0) {
		cout << "Preemption on port " << dev_no << ((on == 1) ? " enabled" : " disabled") << endl  ;
	}
	return (err) ;
}

int EsuQbv::Action(void)
{ 
	ioc_data.error = -1 ;

	if (err == 0 && SetDevice(if_name) == true) {

		if (operation == 5) {			// test peer's preemption capability
			err = DoEnablePreemption(pre_emption) ;
		}
		else if (operation == 4) {			// test peer's preemption capability
			err = DoTestPreemption() ;
		}
		else if (operation == 3) {			// list all sets
			err = DoList() ;
		}
		else if (operation == 2) {		// write operation
			err = WriteIO(dev_no, reg_off, reg_val) ;
			if (err == 0) {
				cout << "Port:" << dev_no << " offset:0x" << hex << setw(8) << setfill('0')
					 << reg_off << " value:0x" << reg_val << endl  ;
				if (tab_set != 0) {
					err = WriteQbvPortTableControl(dev_no, pointer, qstate) ;
					if (err == 0) {
						cout << "Writing port table pointer:0x" << hex << setw(2) << setfill('0') << pointer << endl ;
					}
				}
			}
		}
		else {	// read operation
			if (tab_set != 0) {
				err = ReadQbvPortTableControl(dev_no, pointer) ;
				if (err == 0) {
					cout << "Reading port table pointer:0x" << hex << setw(2) << setfill('0') << pointer << endl ;
				}
			}

			if (err == 0 && operation == 1) {
				err = ReadIO(dev_no, reg_off, &reg_val) ;
				if (err == 0) {
					cout << "Port:" << dev_no << " offset:0x" << hex << setw(8) << setfill('0')
						 << reg_off << " value:0x" << reg_val << endl  ;
				}
			}
		}
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuQbv::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuQbv::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (	strcmp(argv[1], "QBV") == 0 ) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
