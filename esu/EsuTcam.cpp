/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1,   1 },	// Interface name
        { "-list",	0,   0 },	// Read VTU entries
        { "-add",	3,   3 },	// Add  TCAM Entry
        { "-read",	1,   1 },	// Add  TCAM Entry
        { "-actions",	0,   0 },	// List all available actions for add command
        { "-flush",	0,   1 },	// Flush all (FID) entries
        { "-v",		0,   1 },	// verbose option
        { "-D",		1,   1 },	// Data file ...
        { "-A",		1, 255 },	// Action parameter list  ...
} ;

/* **********************************************************************************
 					TCAM ACTIONS
 ************************************************************************************/


#define	TCAM_FIELD_SIZE(f)	(sizeof(f)/sizeof(TCAM_Field_t))

/* TCAM PAGE 0
 */
static TCAM_Field_t	P0_01[] = {
	{ 15,12,	"Block",	0,		},
	{ 11, 5,	"Reserved",	0,		},
	{  4, 0,	"Port",		0,		},
} ;

static TCAM_Field_t	P0_02[] = {
	{ 15,14,	"FrameTypeMask",3,		},
	{ 13,13,	"TimeKeyMask",	0,		},
	{ 12,12,	"Reserved",	0,		},
	{ 11, 8,	"SPV_11_8_Mask",0,		},
	{  7, 6,	"FrameType",	0,		},
	{  5, 5,	"TimeKey",	0,		},
	{  4, 4,	"Reserved",	0,		},
	{  3, 0,	"SPV_11_8",	0,		},
} ;

static TCAM_Field_t	P0_03[] = {
	{ 15, 8,	"SPV_7_0_Mask",	0,		},
	{  7, 0,	"SPV_7_0",	0,		},
} ;

static TCAM_Field_t	P0_04[] = {
	{ 15, 8,	"K3Mask",	0,		},
	{  7, 4,	"PPRI",		0,		},
	{  3, 0,	"PVID_11_8",	0,		},
};

static TCAM_Field_t	P0_05[] = {
	{ 15, 8,	"K4Mask",	0		},
	{  7, 0,	"Index",	0		},
	{  7, 0,	"PVID_7_0",	0		},
};

static TCAM_Register_t	TCAM_P0[] = {
	{ 0, 0, 1, TCAM_FIELD_SIZE(P0_01), P0_01, 0 },
	{ 0, 0, 2, TCAM_FIELD_SIZE(P0_02), P0_02, 0 },
	{ 0, 0, 3, TCAM_FIELD_SIZE(P0_03), P0_03, 0 },
	{ 0, 0, 4, TCAM_FIELD_SIZE(P0_04), P0_04, 0 },
	{ 0, 0, 5, TCAM_FIELD_SIZE(P0_05), P0_05, 0 },
} ;

/* TCAM PAGE 1
 */
// Contains just data and masks

/* TCAM PAGE 2
 */
static TCAM_Field_t	P2_B0_O1[] = {
	{ 15,12,	"Block2",	0,		},
	{ 11, 5,	"Reserved2",	0,		},
	{  4, 0,	"Port2",	0,		},
} ;

static TCAM_Field_t	P2_B0_O2[] = {
	{ 15,13,	"Continue",	0		},
	{ 12,12,	"VIDOverride",	0		},
	{ 11, 0,	"VIDData",	0		},
} ;

static TCAM_Field_t	P2_B0_O3[] = {
	{ 15, 8,	"NextIndex",	0		},
	{ 15, 8,	"FlowId",	0		},
	{  7, 7,	"QPRIOverride",	0		},
	{  6, 4,	"QPRIData",	0		},
	{  3, 3,	"FPRIOverride",	0		},
	{  2, 0,	"FPRIData",	0		},
} ;

static TCAM_Field_t	P2_B0_O4[] = {
	{ 15,15,	"QPRI_AVBOverride",	0	},
	{ 14,12,	"QPRI_AVBData",		0	},
	{ 11,11,	"SourcePortFilter",	0	},
	{ 10, 0,	"DPV_RPV_10_0",		0	},
} ;

static TCAM_Field_t	P2_B0_O5[] = {
	{  0, 0,	"DPV_RPV_11",		0	},
} ;

static TCAM_Field_t	P2_B0_O6[] = {
	{ 15,14,	"DPV_Mode",		0	},
	{ 13,12,	"Color_Mode",		0	},
	{ 11,11,	"VTU_PageOverride",	0	},
	{ 10,11,	"VTU_PageData",		0	},
	{  9, 8,	"UnknownFiltering",	0	},
	{  5, 0,	"EgressActionPointer",	0	},

} ;
static TCAM_Field_t	P2_B0_O7[] = {
	{ 15,15,	"LoadBalanceOverride",	0	},
	{ 14,12,	"LoadBalanceData",	0	},
	{  9, 9,	"IpMc",			0	},
	{  8, 8,	"Ip2Me",		0	},
	{  7, 7,	"RouteEntry",		0	},
	{  6, 6,	"DSCPOverride",		0	},
	{  5, 0,	"DSCPData",		0	},
} ;

static TCAM_Field_t	P2_B0_O8[] = {
	{ 15,15,	"FActionOverride",	0	},
	{ 14, 0,	"FActionData",		0	},
} ;

static TCAM_Field_t	P2_B0_27[] = {
	{ 15,15,	"Int",			0	},
	{ 14,14,	"IncTcamCtr",		0	},
	{ 13,12,	"TcamCtr",		0	},
} ;

static TCAM_Field_t	P2_B0_28[] = {
	{  4, 0,	"Debug_Port",		0	},
} ;

static TCAM_Field_t	P2_B0_31[] = {
	{ 15, 8,	"High_Hit",		0	},
	{  7, 0,	"Low_Hit",		0	},
} ;

static TCAM_Field_t	P2_B6_xx[] = {
} ;

static TCAM_Field_t	P2_B7_xx[] = {
} ;

static TCAM_Register_t	TCAM_P2_B0[] = {
	{ 2, 0,  1, TCAM_FIELD_SIZE(P2_B0_O1), P2_B0_O1, 0 },
	{ 2, 0,  2, TCAM_FIELD_SIZE(P2_B0_O2), P2_B0_O2, 0 },
	{ 2, 0,  3, TCAM_FIELD_SIZE(P2_B0_O3), P2_B0_O3, 0 },
	{ 2, 0,  4, TCAM_FIELD_SIZE(P2_B0_O4), P2_B0_O4, 0 },
	{ 2, 0,  5, TCAM_FIELD_SIZE(P2_B0_O5), P2_B0_O5, 0 },
	{ 2, 0,  6, TCAM_FIELD_SIZE(P2_B0_O6), P2_B0_O6, 0 },
	{ 2, 0,  7, TCAM_FIELD_SIZE(P2_B0_O7), P2_B0_O7, 0 },
	{ 2, 0,  8, TCAM_FIELD_SIZE(P2_B0_O8), P2_B0_O8, 0 },
	{ 2, 0, 27, TCAM_FIELD_SIZE(P2_B0_27), P2_B0_27, 0 },
	{ 2, 0, 28, TCAM_FIELD_SIZE(P2_B0_28), P2_B0_28, 0 },
	{ 2, 0, 31, TCAM_FIELD_SIZE(P2_B0_31), P2_B0_31, 0 },
	{ 2, 6,  1, TCAM_FIELD_SIZE(P2_B6_xx), P2_B6_xx, 0 },	// Block 6: TBD
	{ 2, 7,  1, TCAM_FIELD_SIZE(P2_B7_xx), P2_B7_xx, 0 },	// Block 7: TBD
} ;

/* TCAM PAGE 3
 */
static TCAM_Field_t	P3_B0_O1[] = {
	{ 15,12,	"Block3",		0,		},
	{ 11, 5,	"Reserved3",		0,		},
	{  4, 0,	"Port3",		0,		},
} ;

static TCAM_Field_t	P3_B0_O2[] = {
	{ 14,14,	"FrameModeOverride",	0			},
	{ 13,12,	"FrameModeData",	0			},
	{ 11,11,	"NoTTLDec",		0			},
	{ 10,10,	"TagModeOverride",	0			},
	{  9, 8,	"TagModeData",		0			},
	{  5, 4,	"DAMode",		0			},
	{  2, 0,	"SAMode",		0			},
} ;

static TCAM_Field_t	P3_B0_O3[] = {
	{ 14,14,	"EgressVIDOverride",	0			},
	{ 14,14,	"E-CIDOverride",	0			},
	{ 13,12,	"EgressVIDMode",	0			},
	{ 13,12,	"E-CIDMode",		0			},
	{ 11, 0,	"EgressVIDData",	0			},
	{ 11, 0,	"E-CIDData",		0			},
} ;

static TCAM_Field_t	P3_B0_O4[] = {
	{ 15,14,	"DSCPMode",		0			},
	{ 13, 8,	"EgressDSCP",		0			},
	{  6, 6,	"EgressFPRIOverride",	0			},
	{  5, 4,	"EgressFPRIMode",	0			},
	{  3, 3,	"EgressCFIData",	0			},
	{  2, 0,	"EgressFPRIData",	0			},
} ;

static TCAM_Field_t	P3_B0_O5[] = {
	{  6, 6,	"EgrSIDOverride",	0			},
	{  5, 0,	"EgrSID",		0			},
} ;

static TCAM_Field_t	P3_B0_31[] = {
	{ 13, 8,	"Last_Non-Zero_Hit",	0			},
	{  5, 0,	"Last_Hit",		0			},
} ;

static TCAM_Field_t	P3_B8_xx[] = {
} ;

static TCAM_Register_t	TCAM_P3_B0[] = {
	{ 3, 0,  1, TCAM_FIELD_SIZE(P3_B0_O1), P3_B0_O1, 0 },
	{ 3, 0,  2, TCAM_FIELD_SIZE(P3_B0_O2), P3_B0_O2, 0 },
	{ 3, 0,  3, TCAM_FIELD_SIZE(P3_B0_O3), P3_B0_O3, 0 },
	{ 3, 0,  4, TCAM_FIELD_SIZE(P3_B0_O4), P3_B0_O4, 0 },
	{ 3, 0,  5, TCAM_FIELD_SIZE(P3_B0_O5), P3_B0_O5, 0 },
	{ 3, 0, 31, TCAM_FIELD_SIZE(P3_B0_31), P3_B0_31, 0 },
	{ 3, 8,  2, TCAM_FIELD_SIZE(P3_B8_xx), P3_B8_xx, 0 },	// Block 8: TBD
} ;

#define	TCAM_ACTION_SIZE(f)	(sizeof(f)/sizeof(TCAM_Register_t))

static TCAM_All_Actions_t	TCAM_All_Actions[] = {
	TCAM_ACTION_SIZE(TCAM_P0),	TCAM_P0,
	TCAM_ACTION_SIZE(TCAM_P2_B0),	TCAM_P2_B0,
	TCAM_ACTION_SIZE(TCAM_P3_B0),	TCAM_P3_B0,
} ;


/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     EsuTcam::EsuTcam(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuTcam::EsuTcam(int argc, char* argv[]) : Esu(argc, argv), File(argc, argv)
{
	if_name = NULL ;
	ioc_cmd = 0 ;
	sub_cmd = 0 ;		// list
	ioc_data.offs = 0x10000 ;

	SPV = 0x7FE ;		// all 12 ports, except port 0 (CPU) and 11 (PCI)
	TCAM_Entry = 0 ;
	TCAM_Mode = 3 ;
	Data_File = NULL ;

	bzero (fmask,sizeof(fmask)) ;
	bzero (frame,sizeof(frame)) ;

	// frame[12] = 0x81 ;
	// fmask[12] = 0xff ;
	r_offs = -1 ;

	verbose_flags = 0 ;
}

CliArgs_t * EsuTcam :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuTcam :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-actions", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		sub_cmd = 0 ;	// display action parameter list
		goto end ;
	}

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
		if (num == 1) {
			verbose_flags = StringToDecHexInt(argv[pos]) ;
		}
	}
#if 1
		err = ScanArgList(argc, argv, "-A", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num > 0) {
			err = ReadActionArgumentList(argv, pos, num) ;
		}
		else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
			err = 0 ;
		}
		else {
			goto end ;
		}
#endif

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 4 ;	// get next
		goto end ;
	}

	err = ScanArgList(argc, argv, "-read", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		TCAM_Entry = StringToDecHexInt(argv[pos]) ;
		sub_cmd = 5 ;	// read specific entry
		goto end ;
	}
	
	err = ScanArgList(argc, argv, "-flush", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		if (num == 1) {
			TCAM_Entry = StringToDecHexInt(argv[pos]) ;
			sub_cmd = 2 ;	// Flush all entries
		}
		else {
			sub_cmd = 1 ;	// Flush all entries
		}
		goto end ;
	}

	err = ScanArgList(argc, argv, "-add", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num >= 2) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		TCAM_Entry = StringToDecHexInt(argv[pos]) ;
		SPV = StringToDecHexInt(argv[pos+1]) ;
		TCAM_Mode = StringToDecHexInt(argv[pos+2]) ;
		sub_cmd = 3 ;	// Flush all non-static entries of FID

		err = ScanArgList(argc, argv, "-D", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num == 1) {
			Data_File = argv[pos] ;
		}
		else if (err != IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
			goto end ;
		}
#if 0
		err = ScanArgList(argc, argv, "-A", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK && num > 0) {
			err = ReadActionArgumentList(argv, pos, num) ;
		}
		else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
			err = 0 ;
		}
#endif
		goto end ;
	}
end:
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuTcam::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuTcam::postAction(void)
{ 
}

int EsuTcam::TcamWriteIO(uint32_t reg, uint32_t data)
{
	return (WriteIO(0x1F, reg, data)) ;
}

int EsuTcam::TcamReadIO(uint32_t reg, uint32_t *data)
{
	return (ReadIO(0x1F, reg, data)) ;
}

int EsuTcam :: TcamCmdDone(uint32_t reg, uint32_t b, uint32_t val)
{
	return (CmdDone(0x1F,reg,b,val)) ;
}

int EsuTcam::ExecuteCmd(uint32_t opcode, uint32_t page)
{
	uint32_t command = (1 << 15) | ((opcode & 7) << 12) | ((page & 3) << 10) | ((TCAM_Entry & 0xFF) << 0) ;

	err = TcamWriteIO(0, command) ;
	if (err == 0) {
		err = TcamCmdDone(0, 15, 0) ;
	}
	if (verbose_flags & (1<<2)) {
		cout << "Cmd:" << opcode << " Page:" << page << " Entry#:" << TCAM_Entry << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuTcam::CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val)
{
	uint32_t	data ;
	
	val &= 1 ;
	err = 0 ;
	for (int i=0; i<100 && err==0 ; i++) {
		// err = TcamReadIO(reg, &data) ;
		usleep(100) ;
		err = ReadIO(dev, reg, &data) ;
		if (err == 0 && ((data >> b) & 1) == val) {
			return(0) ;
		}
	}
	if (err == 0) {
		err = IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}
	return (err) ;
}

int EsuTcam::EsuSetPortTcam(uint32_t port, uint32_t mode)
{
	uint32_t data ;

	err = ReadIO(port, 0x0D, &data) ;
	if (err == 0) {
		data &= ~3 ;
		data |= (mode & 3) ;
		err = WriteIO(port, 0x0D, data) ;
		if (verbose) {
			cout << "TCAM mode " << mode << " for port " << port <<
				 " val=0x" << hex << data << endl ;
		}
	}
	return (err) ;
}

int EsuTcam::EsuEnablePortForTcam(uint32_t port_mask)
{
	err = 0 ;
	for (int i=0 ; (err == 0) && (i < (sizeof(port_mask)*8)); i++) {
		if ((port_mask & (1<<i)) != 0) {
			err = EsuSetPortTcam(i,TCAM_Mode) ;
		}
	}
	return (err) ;
}

int EsuTcam::EsuDisablePortForTcam(uint32_t port_mask)
{
	err = 0 ;
	for (int i=0 ; (err == 0) && (i < (sizeof(port_mask)*8)); i++) {
		if ((port_mask & (1<<i)) != 0) {
			err = EsuSetPortTcam(i,0) ;
		}
	}
	return (err) ;
}

void EsuTcam::SetPortMask()
{
	int	 num ;
	uint32_t off ;
	uint32_t vect ;
	uint32_t mask ;

	// Detmine the number of valid ports > 1
	//
	num = 0 ;
	for (off=0; off < 12 && num <= 2; off++) {
		if ((SPV & (1 << off)) != 0) {
			++num ;
		}
	}
	if (num > 1) {			// multiple ports
		mask = ~SPV ;		// bit inverted SPV mask
		vect = 0 ;
	}
	else {				// one or zero ports
		mask = 0xFFF ;
		vect = SPV ;		
	}

	SetActionParameter("SPV_7_0_Mask", mask & 0xFF) ;
	SetActionParameter("SPV_7_0",      vect & 0xFF) ;
	SetActionParameter("SPV_11_8_Mask",(mask >> 8) & 0xF) ;
	SetActionParameter("SPV_11_8",     (vect >> 8) & 0xF) ;

	// SetActionParameter("FrameTypeMask", 3) ;
	// SetActionParameter("FrameType",     FrameType) ;
}

int EsuTcam::GetPortMask()
{
	TCAM_Field_t	*ptr ;
	TCAM_Register_t *reg ;
	uint32_t vect ;
	uint32_t mask ;
	uint32_t data ;

	err = TcamReadIO(0, &data) ;	if (err != 0) return (err) ;
	TCAM_Entry = (data & 0xFF) ;

	err = TcamReadIO(1, &data) ;	if (err != 0) return (err) ;

	err = TcamReadIO(2, &data) ;	if (err != 0) return (err) ;

	reg = TcamGetRegisterPointer(0,0,2) ;
	if (reg->data == 0x00FF) {
		return (-1) ;	// no more valid entry
	}

	ptr = TcamGetRegisterFieldPointer("SPV_11_8_Mask") ;	if (ptr == NULL) return(IOCTL_CMD_STATUS_ERROR) ;
	mask = (ptr->data & 0xF) << 8  ;	// Port mask 8 .. 11

	ptr = TcamGetRegisterFieldPointer("SPV_11_8") ;		if (ptr == NULL) return(IOCTL_CMD_STATUS_ERROR) ;
	vect = (ptr->data & 0xF) << 8 ;

	ptr = TcamGetRegisterFieldPointer("SPV_7_0_Mask") ;	if (ptr == NULL) return(IOCTL_CMD_STATUS_ERROR) ;
	mask |= ptr->data & 0xFF ;		// Port mask 0 .. 7

	ptr = TcamGetRegisterFieldPointer("SPV_7_0") ;		if (ptr == NULL) return(IOCTL_CMD_STATUS_ERROR) ;
	vect |= ptr->data & 0xFF ;		// Port vector 0 .. 7

	ptr = TcamGetRegisterFieldPointer("FrameType") ;		if (ptr == NULL) return(IOCTL_CMD_STATUS_ERROR) ;
	FrameType = (data >> 6) & 3 ;	// Frame type

	if (vect == 0) {
		SPV = ~mask & 0xFFF ;		// bit inverted SPV mask
	}
	else if (mask == 0xFFF) {
		SPV = vect ;		
	}
	else {
		SPV = 0 ;
	}

	if (verbose_flags & (1<<3)) {
		cout << "TCAM port mask: 0x" << hex << setw(3) << setfill('0') << mask <<
			 " vector: 0x" << hex << setw(3) << setfill('0') << vect <<
			 " SPV: 0x" << hex << setw(4) << setfill('0') << SPV << endl ;
	}
	return (0) ;
}

TCAM_Field_t *  EsuTcam::TcamGetRegisterFieldPointer(string s)
{
	int		act, elem, field ;
	TCAM_Field_t	*bit_field = NULL ;

	for (act=0; act < sizeof(TCAM_All_Actions)/sizeof(TCAM_All_Actions_t); act++) {
		TCAM_All_Actions_t *next = &TCAM_All_Actions[act] ;

		for (elem=0; elem < next->Elements; elem++) {
			TCAM_Register_t *tcreg = &next->Action[elem] ;

			for (field = 0; field < tcreg->FieldCnt; field++) {
				TCAM_Field_t *bit_field = &tcreg->TCAMFlds[field] ;

				if (strcmp(bit_field->b_descr,s.c_str()) == 0) {
					if (verbose_flags) {
						// cout << dec <<  "Page: " << tcreg->TcamPage << " Offs:0x" << hex << tcreg->TcamOffs << " " ;
					}
					return (bit_field) ;
				}
			}
		}
	}
	return (NULL) ;
}

bool EsuTcam::SetActionParameter(string s, uint32_t val)
{
	TCAM_Field_t	* bit_field ;

	bit_field = TcamGetRegisterFieldPointer(s) ;
	if (bit_field != NULL) {
		SetActionParameterBits(bit_field, val) ;
		return (true) ;
	}
	return (false) ;
}

void EsuTcam::SetActionParameterBits(TCAM_Field_t *bit_field, uint32_t val)
{
	uint32_t width ;

	width = (1 << (bit_field->b_pos_e - bit_field->b_pos_s + 1)) - 1 ;
	bit_field->data = (val & width) ; // << bit_field->b_pos_s ;

#if 0
	if (verbose_flags) {
		cout << " Bits[" << setfill(' ') << dec << setw(2) << bit_field->b_pos_e
		<< ":" << setfill(' ') << dec << setw(2) << bit_field->b_pos_s << "]"
		<< " data: 0x" << hex << setw(8) << setfill('0') << bit_field->data 
		<< " " <<  bit_field->b_descr << "(0x" << val << ")" << endl ;
	}
#endif
}

bool EsuTcam::SetValidActionParameter(char *ptr)
{
	int	act, elem, field ;

	for (act=0; act < sizeof(TCAM_All_Actions)/sizeof(TCAM_All_Actions_t); act++) {
		TCAM_All_Actions_t *next = &TCAM_All_Actions[act] ;

		for (elem=0; elem < next->Elements; elem++) {
			TCAM_Register_t *tcreg = &next->Action[elem] ;

			for (field = 0; field < tcreg->FieldCnt; field++) {
				TCAM_Field_t *bit_field = &tcreg->TCAMFlds[field] ;

				string s = bit_field->b_descr ;
				string a = ptr ;


				std::size_t pos =  a.find(s+"=") ;


				if (pos == 0) {
					uint32_t val ;
					// cout << dec <<  "SET FIELD: " << s << " ptr=" << a << endl ;
		
					val = StringToDecHexInt(&ptr[s.size()+1]) ;
					if (verbose_flags) {
						cout << dec <<  "Page: " << tcreg->TcamPage << " Offs:0x" << hex << tcreg->TcamOffs << " " ;
					}
					SetActionParameterBits(bit_field, val) ;

					return (true) ;
				}
			}
		}
	}
	return (false) ;
}

int EsuTcam::ReadActionArgumentList(char *argv[], int pos, int num) 
{
	err = 0 ;
	for (int idx = 0 ; idx < num && err == 0; idx++) {
		if (SetValidActionParameter(argv[pos]) == false) {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
		}
		++pos ;
	}
	return (err) ;
}
		

int EsuTcam::TcamPutPageActions(TCAM_Register_t *tcreg)
{
	uint32_t	width ;

	err = 0 ;
	if (tcreg->FieldCnt > 0) {
		tcreg->data = 0 ;
		for (int idx = 0; idx < tcreg->FieldCnt; idx++) {
			TCAM_Field_t *bit_field = &tcreg->TCAMFlds[idx] ;
			tcreg->data |= bit_field->data << bit_field->b_pos_s ;
	 		// cout << "TCAM field: " << bit_field->b_descr << " tcreg->data:" << hex << bit_field->data <<  endl ;
		}
#if 1
		if (verbose_flags) {
			cout << "TCAM page: " << dec << tcreg->TcamPage << " Block: " << tcreg->TcamBlck << 
				" Offset: " << tcreg->TcamOffs << " data: " << hex << setw(8) << setfill('0') <<  tcreg->data << endl ;
		}
#endif
		err = TcamWriteIO(tcreg->TcamOffs, tcreg->data) ;	if (err != 0) return (err) ;
	}

	// data = ((tcreg->TcamBlck & 0xF) << 12) | 0 ;	// Port TBD
	// err = TcamWriteIO(1, data) ;	if (err != 0) return (err) ;

	return (err) ;
}

int EsuTcam::TcamPutListToPage(uint32_t page)
{
	err = 0 ;
	for (int idx=0; idx < sizeof(TCAM_All_Actions)/sizeof(TCAM_All_Actions_t) && err == 0; idx++) {
		int		    elem ;
		TCAM_All_Actions_t *next = &TCAM_All_Actions[idx] ;

		for (elem=0; elem < next->Elements && err == 0; elem++) {
			TCAM_Register_t *reg = &next->Action[elem] ;

			if (page == reg->TcamPage) {
				err = TcamPutPageActions(reg) ;
			}
		}
	}
	return (err) ;
}

int EsuTcam::TcamGetPageActions(TCAM_Register_t *tcreg)
{
	uint32_t	data ;
	uint32_t	width ;

	err = TcamReadIO(tcreg->TcamOffs, &data) ;

	if (err == 0) {
		if (tcreg->FieldCnt > 0) {
			tcreg->data = 0 ;
			// cout << "TcamGetPageActions: Data:" << data << endl ;

			for (int idx = 0; idx < tcreg->FieldCnt; idx++) {
				TCAM_Field_t *bit_field = &tcreg->TCAMFlds[idx] ;
				width = (1 << (bit_field->b_pos_e - bit_field->b_pos_s + 1)) - 1 ;
				bit_field->data = (data >> bit_field->b_pos_s) & width ;
				tcreg->data |= (bit_field->data << bit_field->b_pos_s) ;
			}
			// cout << "TcamGetPageActions: Page:" << tcreg->TcamPage << " Offs:" << tcreg->TcamOffs << " Data:" << hex <<  tcreg->data << endl ;
		}
	}
	return (err) ;
}

int EsuTcam::TcamGetPageToList(uint32_t page)
{
	err = 0 ;
	for (int idx=0; idx < sizeof(TCAM_All_Actions)/sizeof(TCAM_All_Actions_t) && err == 0; idx++) {

		int		    elem ;
		TCAM_All_Actions_t *next = &TCAM_All_Actions[idx] ;

		for (elem=0; elem < next->Elements && err == 0; elem++) {
			TCAM_Register_t *reg = &next->Action[elem] ;

			if (page == reg->TcamPage) {
				err = TcamGetPageActions(reg) ;
			}
		}
	}
	return (err) ;
}

TCAM_Register_t * EsuTcam::TcamGetRegisterPointer(uint32_t page, uint32_t block, uint32_t offs)
{
	for (int idx=0; idx < sizeof(TCAM_All_Actions)/sizeof(TCAM_All_Actions_t); idx++) {

		int		    elem ;
		TCAM_All_Actions_t *next = &TCAM_All_Actions[idx] ;

		for (elem=0; elem < next->Elements; elem++) {
			TCAM_Register_t *action = &next->Action[elem] ;

			if (page == action->TcamPage && block == action->TcamBlck && offs == action->TcamOffs) {
				return (action) ;
			}
		}
	}
	return (NULL) ;
}

int EsuTcam::TcamWritePage_0(uint32_t opcode)
{
	uint32_t reg ;
	uint32_t data ;
	int	 off ;

	err = 0 ;
	off = 0 ;


	// 1. Write first 22 data bytes
	//
	for (reg=6; reg <= 27 && err == 0; reg++) {
		data = ((fmask[off] << 8) | (frame[off] << 0)) & 0xFFFF ;
		err = TcamWriteIO(reg, data) ;
		++off ;
	}

	// Keys register 2 and 3: port vector and mask
	//
	SetPortMask() ;

	// Load page specific actions at register 4 and higher and extension register
	// 
	err = TcamPutListToPage(0) ;		if (err != 0) return (err) ;


	// Write page 0
	//
	return (ExecuteCmd(opcode,0)) ;	// Load page 0
}

int EsuTcam::TcamWritePage_1(uint32_t opcode)
{
	uint32_t reg ;
	uint32_t data ;
	int	 off ;

	err = 0 ;
	off = 22 ;

	// 1. Write next 26 data bytes
	//
	for (reg=2; reg <= 27 && err == 0; reg++) {
		data = ((fmask[off] << 8) | (frame[off] << 0)) & 0xFFFF ;
		err = TcamWriteIO(reg, data) ;
		++off ;
	}
	// Write page 1
	//
	return (ExecuteCmd(opcode, 1)) ;	// Load page 1
}

int EsuTcam::TcamWritePage_2(uint32_t opcode)
{
	// Load page specific actions at register 4 and higher and extension register
	// 
	err = TcamPutListToPage(2) ;		if (err != 0) return (err) ;

	return (ExecuteCmd(opcode, 2)) ;	// Load page 2
}

int EsuTcam::TcamWritePage_3(uint32_t opcode)
{
	// Load page specific actions at register 4 and higher and extension register
	// 
	err = TcamPutListToPage(3) ;		if (err != 0) return (err) ;

	return (ExecuteCmd(opcode, 3)) ;	// Load page 3
}

int EsuTcam::Action(void)
{ 
	if (ioc_cmd == OAK_IOCTL_REG_ESU_REQ && SetDevice(if_name) == true) {
		switch(sub_cmd) {
		case 1:	// FALLTHROUGH
		case 2:	err = DoFlush() ;break ;
		case 3: err = DoAdd() ;  break ;
		case 4: err = DoList() ; break ;
		case 5: err = DoRead() ; break ;
		}
	}
	else if (sub_cmd == 0) {
		err = PrintRegisterList(false) ;
	}
	return (err) ;
}

int EsuTcam::TcamReadPage_0(uint32_t opcode)
{
	uint32_t reg ;
	uint32_t data ;
	int	 off ;

#if 1
	TCAM_Register_t *rg = &TCAM_P0[0] ;
	err = TcamPutPageActions(rg) ;
#endif
	off = 0 ;

	err = ExecuteCmd(opcode,0) ;	if (err != 0) return (err) ;

	// Load page specific actions at register 4 and higher and extension register
	// 
	err = TcamGetPageToList(0) ;		if (err != 0) return (err) ;

	// Keys register 2 and 3: port vector and mask
	//
	err = GetPortMask() ;		if (err != 0) return (err) ;

	// 1. Read first 22 data bytes
	//
	for (reg=6; reg <= 27 && err == 0; reg++) {
		err = TcamReadIO(reg, &data) ;
		if (err == 0) {
			fmask[off] = ((data >> 8) & 0xFF) ;
			frame[off] = ((data >> 0) & 0xFF) ;
		}
		++off ;
	}

	return (err) ;
}
	

int EsuTcam::TcamReadPage_1(uint32_t opcode)
{
	uint32_t reg ;
	uint32_t data ;
	int	 off ;

	err = ExecuteCmd(opcode, 1) ;	// Read page 1

	if (err == 0) {
		off = 22 ;

		// 1. Write next 26 data bytes
		//
		for (reg=2; reg <= 27 && err == 0; reg++) {
			err = TcamReadIO(reg, &data) ;
			fmask[off] = (data >> 8) & 0xFF ;
			frame[off] = (data >> 0) & 0xFF ;
			++off ;
		}
	}
	return (err) ;
}

int EsuTcam::TcamReadPage_2(uint32_t opcode)
{
#if 1
	TCAM_Register_t *rg = &TCAM_P2_B0[0] ;
	err = TcamPutPageActions(rg) ;
#endif
	// Load page specific actions at register 4 and higher and extension register
	// 
	err = ExecuteCmd(opcode, 2) ;	// Read page 2

	if (err == 0) {
		err = TcamGetPageToList(2) ;
	}
	return (err) ;
}

int EsuTcam::TcamReadPage_3(uint32_t opcode)
{
#if 1
	TCAM_Register_t *rg = &TCAM_P3_B0[0] ;
	err = TcamPutPageActions(rg) ;
#endif
	// Load page specific actions at register 4 and higher and extension register
	// 
	err = ExecuteCmd(opcode, 3) ;	// Read page 3

	if (err == 0) {
		err = TcamGetPageToList(3) ;	
	}
	return (err) ;
}

/* Search for one of the strings in buffer:
 *          'D:xxxx: <data bytes> ... \n'
 *  or      'M:xxxx: <data bytes> ... \n'
 *
 * Returns: tag D or M in **tag (default 0)
 *          value of hexadecimal digit xxxx in *offs (default -1)
 *          start of <data bytes> as returned value 
 *          end of data line in **end 
 */
char * EsuTcam :: GetDataOffset(char *ptr, char **end, int *offs, char *tag)
{
	char *start = ptr ;

	*offs = -1 ;
	*tag = 0 ;

	while (ptr < *end) {
		if (*ptr == '\n') {	/* end of data line */
			if (*tag != 0) {
				*end = ptr ;
				return (start) ;	/* tag + eol found */
			}
		}
		/* Tag: M: or D: */
		else if ((ptr[0] == 'D' || ptr[0] == 'M') && ptr[1] == ':') {
			*tag = *ptr ;
			ptr += 2 ;
			start = ptr ;
			while (isxdigit(*start)) {
				++start ;
			}
			if ((start > ptr) && (start[0] == ':')) {
				*start = 0 ;
				*offs = StringToHexInt(ptr) ;	/* end loop */
				ptr = start ;
				++start ;
			}
			else {
				*tag = 0 ;
			}
			
		}
		++ptr ;
	}
	return (ptr) ;
}

char * EsuTcam :: GetDataBytes(char *ptr, char *end, int offs, bool mask)
{
	err = 0 ;
	while (ptr < end && (*ptr != 0) && (*ptr != '\n')) {
		if ((ptr+1)< end && isxdigit(ptr[0]) && isxdigit(ptr[1])) {
			if (offs < sizeof(frame)) {
				int v = StringToHexInt(ptr) ;

				if (mask == false) {
					frame[offs] = (v & 0xFF) ;
					// cout << setw(2) << setfill('0') <<  hex << (v & 0xff) << " " ;
				}
				else if (mask == true) {
					fmask[offs] = (v & 0xFF) ;
					// cout << setw(2) << setfill('0') <<  hex <<  (v & 0xFF) << " " ;
				}
				++offs ;
				++ptr ;
			} else {
				err = IOCTL_CMD_STATUS_ERROR ;
				cerr << "Sorry, Data is exceeding TCAM entry size !" << endl ;
				break ;
			}
		}
		++ptr  ; 	// Skip expressions != [0-F][0-F]
	}
	return (ptr+1) ;
}

/* Parse buffer at ptr for valid offset:data information until end of line or
 * end of buffer (len) is reached.
 * Returns the number of consumed bytes in buffer until end of line.
 * On any error err will be set to value != 0.
 */
int EsuTcam :: ExecuteLine(char *ptr, int len, char *tag)
{
	int	offs = -1 ;
	char	*nxt, *end, *eol ;

	err = 0 ;
	end = &ptr[len] ;
	nxt = ptr ;

	do {
		eol = end;	/* until maximal data length in buffer  */
		nxt = GetDataOffset(nxt, &eol, &offs, tag) ;

		// *nxt = 0 ;

		if (*tag != 0) {
			// *eol = 0 ;
			// cout << "LINE:" << nxt << endl ;	
			nxt = GetDataBytes(nxt, eol, offs, (*tag == 'M') ? true:false) ;
			nxt = ++eol ;
			// cout << "REST:" << nxt << endl ;	
			break ;
		}
		else {
			++nxt ;
		}
	}
	while ((err == 0) && (nxt < end)) ;

	return ((int)(eol-ptr)) ;
}

int EsuTcam::ReadDataFile(void) 
{
	err = OpenFile(Data_File, false) ;
	if (err == 0) {
		int	ret, len = 0  ;
		char	tag = 0  ;

		// cout << "File :" << Data_File << " err:" <<err << endl ;
		r_offs = -1 ;
		do {
                	ret  = ReadFileBin(&file_buf[len],sizeof(file_buf)-len) ;
#if 0
			cout << "LINE:" << file_buf << " len=" << len << endl ;
#endif
			len += ret ;
                	if (len > 0) {
				ret = ExecuteLine(file_buf,len,&tag) ;
				len -= ret ;
				if (len > 0) {
					bcopy (&file_buf[ret],file_buf,len) ;
				}
			}
		} while (ret > 0 && err == 0) ;

		CloseFile() ;

		if (err == 0 && verbose) {
			cout << "TCAM filter at slot #" << dec << TCAM_Entry << endl ;
			DisplayPacketAsHexDump(frame,sizeof(frame), 1, true, 32, 0) ;
			DisplayPacketAsHexDump(fmask,sizeof(fmask), 1, true, 32, 0) ;
			cout << endl ;
		}
	}
	return (err) ;
}

int EsuTcam::DoAdd(void) 
{
	if (Data_File != NULL) {
		err = ReadDataFile() ;		if (err != 0) return (err) ;
	}
	err = EsuDisablePortForTcam(SPV) ;	if (err != 0) return (err) ;
	err = TcamWritePage_3(3) ;		if (err != 0) return (err) ;
	err = TcamWritePage_2(3) ;		if (err != 0) return (err) ;
	err = TcamWritePage_1(3) ;		if (err != 0) return (err) ;
	err = TcamWritePage_0(3) ;		if (err != 0) return (err) ;
	err = EsuEnablePortForTcam(SPV) ;
	return (err) ;
}

int EsuTcam::PrintRegisterList(bool print_data) 
{
	int	tot ;
	int	act, elem, field ;

	cout << "####  Bits Parameter"  << endl ;
	cout << "--------------------"  << endl ;

	tot = 0 ;
	for (act=0; act < sizeof(TCAM_All_Actions)/sizeof(TCAM_All_Actions_t); act++) {
		TCAM_All_Actions_t *next = &TCAM_All_Actions[act] ;

		for (elem=0; elem < next->Elements; elem++) {
			TCAM_Register_t *reg = &next->Action[elem] ;

			for (field = 0; field < reg->FieldCnt; field++) {
				TCAM_Field_t *bit_field = &reg->TCAMFlds[field] ;
				++tot ;
				cout << dec << setw(4) << setfill(' ') << tot << " " ;
				cout << dec << setw(2) << setfill(' ') << bit_field->b_pos_e << ":" ;
				cout << dec << setw(2) << setfill(' ') << bit_field->b_pos_s << " " ;
				cout << setw(24) << left <<  bit_field->b_descr  ;
				if (print_data == true) {
					cout << "0x" << right << hex << setw(4) << setfill('0') << bit_field->data << endl ;
				}
				else {
					cout << endl ;
				}
			}
			if (print_data == true) {
				cout << "Page " << reg->TcamPage << " Offset " << reg->TcamOffs << " data:" <<  hex << setw(8) << setfill('0') << reg->data << endl << endl ;
			}
		}
	}
	return (0) ;
}

int EsuTcam::DoFlush() 
{
	err = EsuDisablePortForTcam(0xFFF) ;
	if (err == 0) {
		err =  ExecuteCmd(sub_cmd,0) ;	// Load page 0
	}
	return (err) ;
}

int EsuTcam::GetNextEntry(uint32_t opcode) 
{
	err = TcamReadPage_0(opcode) ;	if (err != 0) return (err) ;
	if (TCAM_Entry >= 64) {
		return (-1) ;
	}
	err = TcamReadPage_1(5) ;	if (err != 0) return (err) ;
	err = TcamReadPage_2(5) ;	if (err != 0) return (err) ;
	err = TcamReadPage_3(5) ; 
	return (err) ;
}

int EsuTcam::DoRead(void) 
{
	TCAM_Entry &= 0xFF ;

	if (TCAM_Entry == 0) {
		TCAM_Entry = 0xFF ;
	}
	else {
		TCAM_Entry -= 1 ;
	}
	
	if (err == 0) {
		err = GetNextEntry(5) ;
		err = DisplayTcamEntry() ;
	}
	return (err) ;
}

int EsuTcam::DoList(void) 
{
	TCAM_Entry = 0xFF ;	// Start condition for GetNext operation
	do {
		err = GetNextEntry(4) ;
		err = DisplayTcamEntry() ;
	}	while (err == 0 && TCAM_Entry != 0xFF) ;

	return err ;
}

int EsuTcam::DisplayTcamEntry() 
{
	switch (err) {
	case -1:	// FALLTHROUGH
		cout << endl ;
		cout << "TCAM ENTRY: " << hex << TCAM_Entry << endl ;
		err = 0 ;
		break ;
	case  0:
#if 1
		cout << endl ;
		cout << "TCAM ENTRY: " << TCAM_Entry << " SPV: 0x" << hex << setw(4) << setfill('0') << SPV << endl ;
		cout << "Data[0-48]:" ;
		DisplayPacketAsHexDump(frame,sizeof(frame), 1, true, 32, 0) ;
		cout << endl << "Mask[0-48]:" ;
		DisplayPacketAsHexDump(fmask,sizeof(fmask), 1, true, 32, 0) ;
		cout << endl ;
		err = PrintRegisterList(true) ;
#endif
		break ;
	default:
		break ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuTcam::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuTcam::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 2) {
		if (strcmp(argv[1], "TCAM") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
