/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef QBVVAL_IO_H
#define QBVVAL_IO_H

#include "Socket.h"
#include "oak_ioc_reg.h"


/* Type definitions included from file: register/RegisterIO.h
 */
typedef	REG_Field_t		QBV_Field_t ;
typedef	REG_Register_t		QBV_Register_t ;
typedef	REG_All_Registers_t	QBV_All_Registers_t ;

class EsuQbvVal : public EsuQbv
{
	public:
				EsuQbvVal(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;

		int		PrintRegisterList(bool print_data) ;
		void 		WriteRegisterList() ;

	static	bool		isResponsible(int argc, char* argv[]) ;

	protected:
		void 		WriteRegister(uint32_t idx) ;
		int		ModifySpecificRegisterData(void) ;

	private:
		uint32_t	sub_cmd ;

} ;

#endif // QBVVAL_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
