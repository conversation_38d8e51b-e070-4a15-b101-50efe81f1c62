/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef QBV_IO_H
#define QBV_IO_H

#include "Socket.h"
#include "oak_ioc_reg.h"

class EsuQbv : public Esu
{
	public:
				EsuQbv(int argc, char* argv[]) ;
		CliArgs_t *	SetArgumentList(uint32_t *arg_list_size) ;
		int 		EvaluateArgumentList(int argc, char *argv[]) ;
		int		postAction(void) ;
		int		Action(void) ;
		

	static	bool		isResponsible(int argc, char* argv[]) ;

	protected:
		int 		ExecuteCmd(uint32_t port, uint32_t dev, uint32_t reg) ;
		int		WriteIO(uint32_t port, uint32_t reg, uint32_t data) ;
		int		ReadIO(uint32_t port, uint32_t reg, uint32_t *data) ;
		int 		WriteQbvPortTableControl(uint32_t port, uint32_t ptr, uint32_t qstate) ;
		int 		ReadQbvPortTableControl(uint32_t port, uint32_t ptr) ;
		int 		QbvCmdDone(uint32_t port, uint32_t reg, uint32_t b, uint32_t val) ;
		int 		DoList(void) ;
		int 		DoListGlobal(void) ;
		int 		DoListPort(void) ;
		int 		DoListTableEntry(uint32_t ptr, uint32_t set) ;
		int 		DoListGuardBand(uint32_t ptr) ;
		int 		DoListTable(uint32_t set) ;
		int             GetPtpGlobalTime(uint32_t *tlo, uint32_t *thi) ;
		int		DoTestPreemption() ;
		int		DoEnablePreemption(uint32_t on) ;

		char	*	if_name ;
		uint32_t	dev_no ;
		uint32_t	pointer ;
		uint32_t	tab_set ;
		EsuTai          *pEsuTai ;

		/* 0:normal register access, 1:read-modify-write-or, 2:read-modify-write-and */
		int		ioc_flag ;
		uint32_t	operation ;
	private:
		uint32_t	qstate ;
		uint32_t	reg_off ;
		uint32_t	reg_val ;
		uint32_t	pre_emption ;

} ;

#endif // QBV_IO_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
