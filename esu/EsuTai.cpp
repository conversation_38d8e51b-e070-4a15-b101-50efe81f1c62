/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1,  1 },	// Interface name
        { "-list",	0,  0 },	// List QBV sets
        { "-rd",	0,  1 },	// Default: read operation
        { "-wr",	0,  2 },	// write operation
        { "-A",		0,128 },	// Set TAI bit fields
        { "-v",		0,  0 },	// verbose option
} ;

static TAI_Field_t     R_00[] = {
	{ 15, 15,        "EventCapOv",		0, },
	{ 14, 14,        "EventCtrStart",	0, },
	{ 13, 13,        "EventPhase",		0, },
	{ 12, 12,        "TrigPhase",		0, },
	{ 11, 11,        "IRLClkGenReq",	0, },
	{  9,  9,        "TrigGenIntEn",	0, },
	{  8,  8,        "EventCapIntEn",	0, },
	{  7,  7,        "TrigLock",		0, },
	{  6,  4,        "TrigLockRange",	0, },
	{  3,  3,        "BlockUpdate",		0, },
	{  2,  2,        "MultiPTPSync",	0, },
	{  1,  1,        "TrigMode",		0, },
	{  0,  0,        "TrigGenReq",		0, },
} ;

static TAI_Field_t     R_01[] = {
	{ 15,  0,        "TSClkPer",		0, },
} ;

static TAI_Field_t     R_02[] = {
	{ 15,  0,        "TrigGenAmtLo",	0, },
} ;

static TAI_Field_t     R_03[] = {
	{ 15,  0,        "TrigGenAmtHi",	0, },
} ;

static TAI_Field_t     R_04[] = {
	{ 15, 15,        "TrigCompDir",		0, },
	{ 14,  0,        "TrigClkComp",		0, },
} ;

static TAI_Field_t     R_05[] = {
	{ 15, 12,        "PulseWidth",		0, },
	{ 10,  8,        "PulseWidthRange",	0, },
	{  7,  0,        "TrigClkCompSubPs",	0, },
} ;

static TAI_Field_t     R_06[] = {
	{ 15,  0,        "IRLClkGenAmt",	0, },
} ;

static TAI_Field_t     R_07[] = {
	{ 15, 15,        "IRLCompDir",		0, },
	{ 14,  0,        "IRLClkComp",		0, },
} ;

static TAI_Field_t     R_08[] = {
	{  7,  0,        "IRLClkCompSubPs",	0, },
} ;

static TAI_Field_t     R_09[] = {
	{ 14, 14,        "CaptureTrig",		0, },
	{  9,  9,        "EventCapErr",		0, },
	{  8,  8,        "EventCapValid",	0, },
	{  7,  0,        "EventCapCtr",		0, },
} ;

static TAI_Field_t     R_10[] = {
	{ 15,  0,        "EventCapRegisterLo",	0, },
} ;

static TAI_Field_t     R_11[] = {
	{ 15,  0,        "EventCapRegisterHi",	0, },
} ;

static TAI_Field_t     R_14[] = {
	{ 15,  0,        "PTPGlobalTimeLo",	0, },
} ;

static TAI_Field_t     R_15[] = {
	{ 15,  0,        "PTPGlobalTimeHi",	0, },
} ;

static TAI_Field_t     R_16[] = {
	{ 15,  0,        "TrigGenTimeLo",		0, },
} ;

static TAI_Field_t     R_17[] = {
	{ 15,  0,        "TrigGenTimeHi",		0, },
} ;

static TAI_Field_t     R_18[] = {
	{  4,  4,        "LockCorrValid",		0, },
	{  3,  0,        "LockCorrection",		0, },
} ;

static TAI_Field_t     R_19[] = {
	{ 15,  0,        "TrigGenDelay",		0, },
} ;

static TAI_Field_t     R_20[] = {
	{ 15,  0,        "IRLGenTimeLo",		0, },
} ;

static TAI_Field_t     R_21[] = {
	{ 15,  0,        "IRLGenTimeHi",		0, },
} ;

static TAI_Field_t     R_22[] = {
	{  4,  4,        "IRLLockCorrValid",		0, },
	{  3,  0,        "IRLLockCorrection",		0, },
} ;

static TAI_Field_t     R_30[] = {
	{ 14, 14,        "PtpExtClk",			0, },
	{ 13, 13,        "TrigWindow",			0, },
	{ 12,  8,        "SecRecClkSel",		0, },
	{  7,  7,        "Trig2Signal",			0, },
	{  4,  0,        "PriRecClkSel",		0, },
} ;

static TAI_Field_t     R_31[] = {
	{ 11, 11,        "TimeIncDecOp",		0, },
	{ 10,  0,        "TimeIncDecAmt",               0, }, 
} ;

static TAI_Register_t  T_REGS[] = {
        { 0, REG_FIELD_SIZE(R_00), R_00, 0, 0 },
        { 1, REG_FIELD_SIZE(R_01), R_01, 0, 0 },
        { 2, REG_FIELD_SIZE(R_02), R_02, 0, 0 },
        { 3, REG_FIELD_SIZE(R_03), R_03, 0, 0 },
        { 4, REG_FIELD_SIZE(R_04), R_04, 0, 0 },
        { 5, REG_FIELD_SIZE(R_05), R_05, 0, 0 },
        { 6, REG_FIELD_SIZE(R_06), R_06, 0, 0 },
        { 7, REG_FIELD_SIZE(R_07), R_07, 0, 0 },
        { 8, REG_FIELD_SIZE(R_08), R_08, 0, 0 },
        { 9, REG_FIELD_SIZE(R_09), R_09, 0, 0 },
        {10, REG_FIELD_SIZE(R_10), R_10, 0, 0 },
        {11, REG_FIELD_SIZE(R_11), R_11, 0, 0 },

        {14, REG_FIELD_SIZE(R_14), R_14, 0, 0 },
        {15, REG_FIELD_SIZE(R_15), R_15, 0, 0 },
        {16, REG_FIELD_SIZE(R_16), R_16, 0, 0 },
        {17, REG_FIELD_SIZE(R_17), R_17, 0, 0 },
        {18, REG_FIELD_SIZE(R_18), R_18, 0, 0 },
        {19, REG_FIELD_SIZE(R_19), R_19, 0, 0 },
        {20, REG_FIELD_SIZE(R_20), R_20, 0, 0 },
        {21, REG_FIELD_SIZE(R_21), R_21, 0, 0 },
        {22, REG_FIELD_SIZE(R_22), R_22, 0, 0 },
        {30, REG_FIELD_SIZE(R_30), R_30, 0, 0 },
        {31, REG_FIELD_SIZE(R_31), R_31, 0, 0 },
} ;

static REG_All_Registers_t	TAI_All_Registers[] = {
	{ /* 0 */ REG_REGISTER_SIZE(T_REGS),   T_REGS	},	// port specific registers
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     EsuTai::EsuTai(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuTai::EsuTai(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;

	// ioc_data.offs = 0x10000 ;
	ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
}

CliArgs_t * EsuTai :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuTai :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;	// List one or all sets
	if (err == IOCTL_CMD_STATUS_OK) {
		operation = 3 ;
		return (err) ;
	}

	err = ScanArgList(argc, argv, "-wr", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		if (num == 2) {
			reg_off = StringToDecHexInt(argv[pos]) ;
			reg_val = StringToDecHexInt(argv[pos+1]) ;
			operation = 2 ;	// Write register operation
		}
		else {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND;	
			return (err) ;
		}
	}
	else {

		err = ScanArgList(argc, argv, "-rd", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			if (num == 1) {
				reg_off = StringToDecHexInt(argv[pos]) ;
				operation = 1 ;	// Read register operation
			}
		}
		else {
			err = ScanArgList(argc, argv, "-A", &pos, &num) ;
                	if (err == IOCTL_CMD_STATUS_OK && num > 0) {
                        	err = ReadActionArgumentList(&argv[pos], num,
                                	TAI_All_Registers, sizeof(TAI_All_Registers)/sizeof(TAI_All_Registers_t)) ;
				operation = 4 ;	// Set register bit fields 
                	}
                	else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
                        	err = IOCTL_CMD_STATUS_OK ;
                	}
		}
	}

	return (err) ;
}

int EsuTai::ExecuteCmd(uint32_t rw, uint32_t port, uint32_t reg)
{
	uint32_t cmd ;	// G2 AVB/TSN command register: command

	// cmd := Bit 15 + rw-cmd + AvbPort=0x1E + AvbBlock=0 + AVB register
	//
	cmd = (1 << 15) | ((rw & 3) << 13) | (port << 8) | (0 << 5) | (reg & 0x1F) ;
	err = Esu::WriteIO(0x1C, 0x16, cmd) ;
	if (err == 0) {
		err = Esu::CmdDone(0x1C, 0x16, 15, 0) ;
	}
	return (err) ;
}

int EsuTai::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	// uint32_t port = 0x1E ;			// TAI: AVB Block == 0x1E

	err = Esu::WriteIO(0x1C, 0x17, data) ;	// Write data
	if (err == 0) {
		err = ExecuteCmd(3, dev, reg) ;
	}
	if (verbose) {
		cout << "WriteTai: port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << data << " err:" << err << endl ;
	}
	return (err) ;
}

int EsuTai::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	// uint32_t port = 0x1E ;			// TAI: AVB Block == 0x1E

	err = ExecuteCmd(0, dev, reg) ;
	if (err == 0) {
		err = Esu::ReadIO(0x1C, 0x17, data) ;	// Read data
	}
	if (verbose) {
		cout << "ReadTai : port=0x" << hex << dev << " reg=0x" << hex << setw(8) << setfill('0') << reg <<
			 " data:0x" << *data << " err:" << err << endl ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuTai::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuTai::postAction(void)
{ 
	return (err) ;
}

int EsuTai::DoListTai()
{
	err = PrintRegisterList(0x1E, TAI_All_Registers, sizeof(TAI_All_Registers)/sizeof(REG_All_Registers_t));
}

int EsuTai::WriteAllRegisterList(uint32_t port)
{
	SetAllRegisterData(TAI_All_Registers,sizeof(TAI_All_Registers)/sizeof(REG_All_Registers_t)) ;

	for (uint32_t idx = 0; err == 0 && (idx < sizeof(TAI_All_Registers)/sizeof(REG_All_Registers_t)); idx++) {
		err =  Esu::WriteRegisterList(port, &TAI_All_Registers[idx]) ;
	}
	return (err) ;
}

int EsuTai::Action(void)
{ 
	uint32_t port = 0x1E ;			// TAI: AVB Block == 0x1E
	ioc_data.error = -1 ;

	if (err == 0 && SetDevice(if_name) == true) {

		if (operation == 4) {			// list all sets
			err = WriteAllRegisterList(port) ;
		}
		else if (operation == 3) {			// list all sets
			err = DoListTai() ;
		}
		else if (operation == 2) {		// write operation
			err = WriteIO(port, reg_off, reg_val) ;
			if (err == 0) {
				cout << "TAI offset:0x" << hex << setw(8) << setfill('0')
					 << reg_off << " value:0x" << reg_val << endl  ;
			}
		}
		else {	// read operation

			if (operation == 1) {
				err = ReadIO(port, reg_off, &reg_val) ;
				if (err == 0) {
					cout << "TAI offset:0x" << hex << setw(8) << setfill('0')
						 << reg_off << " value:0x" << reg_val << endl  ;
				}
			}
		}
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuTai::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuTai::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (	strcmp(argv[1], "TAI") == 0 ) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
