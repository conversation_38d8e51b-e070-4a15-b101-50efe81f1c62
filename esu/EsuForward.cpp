/*******************************************************************************
 *
 * EsuForward.cpp
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;


static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-set",	2, 3 },	// Set mode
        { "-get",	1, 1 },	// Get mode
} ;



/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       设置转发规则
 * @details     EsuForward::EsuForward(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to set or get ESU forward configuration.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuForward::EsuForward(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	ioc_cmd = 0 ;
	sub_cmd = 0 ;
	port_to_read = -1 ;
	ioc_data.offs = 0x10000 ;
}



CliArgs_t * EsuForward :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

	*arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
	return (my_arg_list) ;
}

void EsuForward::ParsePorts(const char* portStr) {
    char* str = strdup(portStr);
    char* token = strtok(str, ",");
    
    while (token != NULL) {
        while (*token && isspace(*token)) token++;
        int port;
        if (strncmp(token, "0x", 2) == 0 || strncmp(token, "0X", 2) == 0) {
            port = strtol(token + 2, NULL, 16);
        } else {
            char* endptr;
            port = strtol(token, &endptr, 16);
        }
        
        if (port >= 0) {
            ports.push_back(mapExternalToInternal(port));
        }
        token = strtok(NULL, ",");
    }
    
    free(str);
}
int EsuForward :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;
	char *value;

    // Parse interface name
    err = ScanArgList(argc, argv, "-if", &pos, &num);
    if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
        return (err);
    }
    else if (num > 0) {
        if_name = argv[pos];
    }


	// Parse set command
	err = ScanArgList(argc, argv, "-set", &pos, &num);
	if (err == IOCTL_CMD_STATUS_OK) {
		sub_cmd = 1;  // set mode
		ports.clear();  // 清空端口列表
		
		// Parse arguments
		for(int i = pos; i < argc; i++) {
			if(strncmp(argv[i], "PORTS=", 6) == 0) {
				ParsePorts(argv[i] + 6);
			}
			else if(strncmp(argv[i], "HOST=", 5) == 0) {
				host = StringToDecHexInt(argv[i] + 5);
			}
		}
		
		if(ports.size() < 1) {  // 至少需要一个端口
			return IOCTL_CMD_STATUS_ERROR_INVALID_NUM_CMDLINE_PARAMS;
		}
		
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ;
		return IOCTL_CMD_STATUS_OK;
	}
		
 	// Parse get command
    err = ScanArgList(argc, argv, "-get", &pos, &num);
    if (err == IOCTL_CMD_STATUS_OK) {
        sub_cmd = 0;  // get mode
        if (num == 1) {
            port_to_read = mapExternalToInternal(StringToDecHexInt(argv[pos]));
        }
        ioc_cmd = OAK_IOCTL_REG_ESU_REQ;
    }


end:
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuStat::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuForward::postAction(void)
{ 
	return (err) ;
}

int EsuForward::ExecuteCmd(uint32_t port, uint32_t command)
{
	err = WriteIO(port, 0x06, command) ;
	return (err) ;
}


int EsuForward::DoSet(void)
{
	for (int port : ports) {
		uint32_t command = 0;
		command |= (host & 0x1) << 16;
		command |= (1 << 11);
		for(uint32_t other_port : ports) {
			if(other_port != port) {
				if(other_port <= 10) {
					command |= (1 << other_port);
				}
			}
		}
		err = ExecuteCmd(port, command) ;
		if (err != IOCTL_CMD_STATUS_OK) {
			return err ;
		}
	}
	return err ;
}

int EsuForward::DoGet(void)
{
    uint32_t res = IOCTL_CMD_STATUS_OK;
    err = ReadIO(port_to_read, 0x06, &res);
    if (err == IOCTL_CMD_STATUS_OK) {
        cout << "    Port " << mapInternalToExternal(port_to_read) << " Forced forwarding: " << ((res >> 11) & 0x1 ? "enabled" : "disabled") << endl;
		if (!((res >> 11) & 0x1)) return err;  // bridge mode 为 disabled 时直接返回
        cout << "    Forward to: ";
        cout << (((res >> 16) & 0x1) ? "HOST " : " ");
		for(int i = 1; i <= 10; i++) {
            if(res & (1 << i)) {
                cout << mapInternalToExternal(i) << " ";
            }
        }
        cout << endl;
    }
    return err;
}

int EsuForward::Action(void)
{ 
	err = IOCTL_CMD_STATUS_ERROR ;

	if (ioc_cmd == OAK_IOCTL_REG_ESU_REQ && SetDevice(if_name) == true) {
		switch(sub_cmd) {
		case 1: err = DoSet() ;break ;
		case 0: err = DoGet() ;break ;
		default: err = IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND ;
		}
	}
	return (err) ;
}



/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuForward::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuForward::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 2) {
		if (strcmp(argv[1], "FORWARD") == 0 || strcmp(argv[1], "forward") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
