/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1, 1 },	// Interface name
        { "-list",	0, 0 },	// Read VTU entries
        { "-flush",	0, 0 },	// Flush all (FID) entries
        { "-clear",	1, 1 },	// Flush all (FID) entries
        { "-add",	1, 5 },	// Flush  non static (FID) entries
        { "-q",		1, 1 },	// Enable 802.1Q mode on ports
        { "-v",		0, 0 },	// verbose option
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     EsuVtu::EsuVtu(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuVtu::EsuVtu(int argc, char* argv[]) : Esu(argc, argv)
{
	if_name = NULL ;
	ioc_cmd = 0 ;
	sub_cmd = 0 ;		// list
	fid = 0 ;		// FID == 0 (common)
	vector = 0x33333333333;	// Frame exit 'unmodified' on all 11 ports
	ioc_data.offs = 0x10000 ;
	qpri = 0 ;
	use_qpri = 0 ;	// Overwrite QPRI
	fpri = 0 ;
	use_fpri = 0 ;	// Overwrite FPRI
	port_qmode = 0 ;
	use_qmode = 0 ;
}

CliArgs_t * EsuVtu :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}

int EsuVtu :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = ScanArgList(argc, argv, "-if", &pos, &num) ;

	if ((if_name == NULL) && (err != IOCTL_CMD_STATUS_OK)) {
		return (err) ;
	}
	else if (num > 0) {
		if_name = argv[pos] ;
	}

	err = ScanArgList(argc, argv, "-v", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		verbose = true ;
	}

	err = ScanArgList(argc, argv, "-list", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 4 ;	// get next
		goto end ;
	}
	
	err = ScanArgList(argc, argv, "-flush", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 1 ;	// Flush all entries
		goto end ;
	}
	err = ScanArgList(argc, argv, "-add", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num > 0) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 3 ;	// Flush all non-static entries of FID
		vid = StringToDecHexInt(argv[pos]) ;

		if (num >= 2) {
			fid = StringToDecHexInt(argv[pos+1]) ;
		}

		if (num >= 3) {
			vector = StringToDecHexLongLong(argv[pos+2]) ;
		}

		if (num >= 4) {
			qpri = StringToDecHexInt(argv[pos+3]) ;
			use_qpri = 1 ;
		}

		if (num == 5) {
			fpri = StringToDecHexInt(argv[pos+4]) ;
			use_fpri = 1 ;
		}

		goto end ;
	}

	err = ScanArgList(argc, argv, "-q", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		port_qmode = StringToDecHexLongLong(argv[pos]) ;
		use_qmode = 1 ;
		goto end ;
	}

	err = ScanArgList(argc, argv, "-clear", &pos, &num) ;
	if (err == IOCTL_CMD_STATUS_OK && num == 1) {
		ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
		sub_cmd = 0 ;	// Flush all non-static entries of FID
		vid = StringToDecHexInt(argv[pos]) ;
	}
end:
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuVtu::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuVtu::postAction(void)
{ 
}

#if 0
int EsuVtu::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_WR ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	ioc_data.data = data ;
	return (Ioctl(ioc_cmd, (caddr_t) &ioc_data)) ;
}

int EsuVtu::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_RD ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	err =  Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
	*data = ioc_data.data ;
	// cout << "Read: " << err << " data=" << hex << setw(4) << *data << endl ;
	return (err) ;
}
#endif

int EsuVtu::ExecuteCmd(uint32_t cmd)
{
	uint32_t command = (1 << 15) | ((cmd & 7) << 12) ;

	err = WriteIO(0x1B, 0x05, command) ;
	if (err == 0) {
		err = CmdDone(0x1B, 0x05, 15, 0) ;
	}
	// cout << "Cmd: " << cmd << " err=" << err << endl ;
	return (err) ;
}

int EsuVtu::CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val)
{
	uint32_t	data ;
	
	val &= 1 ;
	err = 0 ;
	for (int i=0; i<100 && err==0 ; i++) {
		err = ReadIO(dev, reg, &data) ;
		if (err == 0 && ((data >> b) & 1) == val) {
			return(0) ;
		}
		usleep(100) ;
	}
	if (err == 0) {
		err = IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}
	return (err) ;
}

int EsuVtu::Action(void)
{ 
	if (ioc_cmd == OAK_IOCTL_REG_ESU_REQ && SetDevice(if_name) == true) {
		switch(sub_cmd) {
		case 0:	err = DoClear() ;break ;
		case 1:	err = DoFlush() ;break ;
		case 3: err = DoAdd() ;  break ;
		case 4: err = DoList() ; break ;
		}
	}
	if (use_qmode != 0) {
		err = SetQMode() ;
	}
	return (err) ;
}

int EsuVtu::SetQMode(void) 
{
	for (int port=0 ; port<=11; port++) {
		uint32_t mode ;
		err = ReadIO (port, 0x08, &mode) ;	if (err != 0) return(err) ;
		mode &= ~(3 << 10) ;
		mode |= (((port_qmode >> (4*port)) & 3) << 10) ;
		err = WriteIO(port, 0x8, mode) ;	if (err != 0) return(err) ;
		if (verbose) {
			cout << "Port[" << setw(2) << dec << port << "]: port mode: 0x " << hex << mode << endl ;
		}
	}
	return (err) ;
}

int EsuVtu::DoAdd(void) 
{
	uint32_t  data ;

	// cout << "FID: " << hex << fid << endl ;
	err = WriteIO(0x1B, 0x02, (fid & 0xFFF)) ;		if (err != 0) return(err) ;
	err = WriteIO(0x1B, 0x3,0)  ;				if (err != 0) return(err) ;

	// vid includes page bit 13
	err = WriteIO(0x1B, 0x6, (vid & 0x2FFF) | (1 << 12)) ;	if (err != 0) return(err) ;

	data = 0 ;
	for (int port=0; port <= 7; port++) {
		data |= (((vector >> (port*4)) & 3) << (port * 2)) ;
	}
	data |= ((vector >> (11*4)) & 3) << 16 ;

	// Write port membership for port 0 to 7 and 11
	err = WriteIO(0x1B, 0x7, data) ;		if (err != 0) return(err) ;

	data = 0 ;
	if (use_fpri == 1) {
		data |= ((fpri & 7) << 8) | (1 << 11) ;
	}
	if (use_qpri == 1) {
		data |= ((qpri & 7) << 12) | (1 << 15) ;
	}
	// Write port membership for port 8 9 10
	for (int port=8; port <= 10; port++) {
		data |= (((vector >> (port*4)) & 3) << ((port-8) * 2)) ;
	}
	err = WriteIO(0x1B, 0x08, data) ;		if (err != 0) return(err) ;

	if (err == 0) {
		err = ExecuteCmd(sub_cmd) ;
	}

	return (err) ;
}

int EsuVtu::DoClear(void) 
{
	// cout << "FID: " << hex << fid << endl ;

	err = WriteIO(0x1B, 0x03, 0) ;				if (err != 0) return(err) ;

	// vid includes page bit 13
	err = WriteIO(0x1B, 0x6, (vid & 0x2FFF) | (0 << 12)) ;	if (err != 0) return(err) ;

	if (err == 0) {
		err = ExecuteCmd(3) ;
	}

	return (err) ;
}

int EsuVtu::DoFlush() 
{
	if (err == 0) {
		err = ExecuteCmd(sub_cmd) ;
	}
	return (err) ;
}

int EsuVtu::GetNextEntry(uint32_t idx, uint32_t *tot) 
{
	uint32_t	w0, w1, w2 ;
	uint32_t	r_fid, r_vid, r_opc ;
	uint32_t	r_ms0, r_ms1 ;
	// string s ;

	// ATU read command
	err = ExecuteCmd(sub_cmd) ;	if (err != 0) return(-1) ;

	err = ReadIO(0x1B, 0x06, &r_vid) ; if (err != 0) return(-1) ;

	if (r_vid & (1 << 12)) {

		err = ReadIO(0x1B, 0x02, &r_fid) ; if (err != 0) return(-1) ;
		err = ReadIO(0x1B, 0x07, &r_ms0) ; if (err != 0) return(-1) ;
		err = ReadIO(0x1B, 0x08, &r_ms1) ; if (err != 0) return(-1) ;

		cout << dec << setw(5) << setfill(' ') << idx << " " ;
		cout << hex << setw(4) << setfill(' ') << ((r_vid >> 13) & 1) << " "  ;
		cout << hex << setw(4) << setfill(' ') << (r_vid & 0xfff) << " "  ;
		cout << hex << setw(3) << setfill(' ') << (r_fid & 0xfff) << "  "  ;

		// Display membership of port 11
		cout << hex << setw(2) << setfill(' ') << ((r_ms0 >> 16) & 0x3) << " "  ;

		// Display membership of ports 10, 9, 8
		for (int port=2 ; port >= 0; port--) {
			cout << hex << setw(2) << setfill(' ') << ((r_ms1 >> (port*2)) & 0x3) << " "  ;
		}

		// Display membership of ports 7 to 0
		for (int port=7 ; port >= 0; port--) {
			cout << hex << setw(2) << setfill(' ') << ((r_ms0 >> (port*2)) & 0x3) << " "  ;
		}

		cout << " " ;

		// Display overwrite QPRI
		if (r_ms1 & (1 << 15)) {
			cout << hex << setw(4) << setfill(' ') << ((r_ms1 >> 12) & 7) << " "  ;
		}
		else {
			cout << hex << "  no "  ;
		}

		// Display overwrite FPRI
		if (r_ms1 & (1 << 11)) {
			cout << hex << setw(4) << setfill(' ') << ((r_ms1 >>  8) & 7) ;
		}
		else {
			cout << hex << "  no"  ;
		}
		cout << endl ;
		*tot += 1 ;
	}

        if ((r_vid & 0xfff) == 0xfff) {
                return (0) ;
        }
        return (1) ;
}

int EsuVtu::DoList(void) 
{
	uint32_t	cnt ;
	uint32_t	tot ;
	uint32_t	pg ;
	int		rc ;

	cnt = 0 ;
	tot =  0 ;

	cout << endl ;
	cout << "Entry Page VLAN FID             Port Membership           Overwrite" << endl ;
	cout << "-------------------  11 <USER> <GROUP> 08 07 06 05 04 03 02 01 00  QPRI FPRI" << endl ;
	cout << endl ;

	for (pg=0; pg < 2 ; pg++) {
		err = WriteIO(0x1B, 0x06, (pg << 13) | 0x0fff) ; if (err != 0) return(err) ;

		rc = 1 ;
		while (rc == 1) {
			rc = GetNextEntry(cnt, &tot) ;
			++cnt ;
		}
	}
	if (tot == 0) {
		cout << "VLAN table is empty." << endl ;
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuVtu::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuVtu::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (argc >= 2) {
		if (strcmp(argv[1], "VTU") == 0) {
			return true;
		}
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
