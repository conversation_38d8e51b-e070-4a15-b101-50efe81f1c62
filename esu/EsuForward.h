/*******************************************************************************
 *
 * EsuForward.h
 *
 ******************************************************************************/

#ifndef ESUFORWARD_H
#define ESUFORWARD_H

#include "Socket.h"
#include "oak_ioc_reg.h"
#include <vector>
#include <map>

class EsuForward : public Esu
{
	public:
		EsuForward(int argc, char* argv[]) ;
		CliArgs_t *	    SetArgumentList(uint32_t *arg_list_size) ;
		int 		    EvaluateArgumentList(int argc, char *argv[]) ;
		int		        postAction(void) ;
		int	        	Action(void) ;
		void	    	SetIfName(char *ifn) { if_name = ifn ; }
		static bool     isResponsible(int argc, char* argv[]) ;


	protected:
		int		        ExecuteCmd(uint32_t port, uint32_t command) ;
		int             DoSet(void) ;
		int             DoGet(void) ;
		void            ParsePorts(const char* portStr) ;
	private:
		uint32_t        port_to_read;
		std::vector<uint32_t> ports;
		uint32_t        host;
		char*           if_name ;
		uint32_t        sub_cmd ;

		/*
		 * 0:normal register access, 
		 * 1:read-modify-write-or, 
		 * 2:read-modify-write-and 
		 */
		int		ioc_flag ;
} ;



#endif // ESUFORWARD_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
