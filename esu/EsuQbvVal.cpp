/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;

static CliArgs_t my_arg_list[] = {
        { "-if",	1,  1 },	// Interface name
        { "-dev",	1,  1 },	// Device,Port number
        { "-list",	0,  0 },	// List QBV sets
        { "-te",	0,  1 },	// Table entry number
        { "-gb",	0,  0 },	// Guard band 
        { "-set",	1,  1 },	// Set number
        { "-A",		0,128 },	// Action parameter list
        { "-v",		0,  0 },	// verbose option
}  ;

#define QBV_FIELD_SIZE(f)      (sizeof(f)/sizeof(QBV_Field_t))

static QBV_Field_t     P_00[] = {
        { 15, 0,        "PortDelay1",        0,              },
} ;

static QBV_Field_t     P_01[] = {
        { 15, 0,        "PortDelay2",        0,              },
} ;

static QBV_Field_t     P_02[] = {
        {  7, 0,        "QueueState",        0xff,           },
} ;

static QBV_Field_t     P_03TE[] = {
        { 15,15,        "GuardBand",         0,              },
        { 14, 0,        "WindowTime",        0,              },
} ;

static QBV_Field_t     P_03GB[] = {
        { 13, 0,        "GBBytes",           0,              },
} ;

static QBV_Field_t     P_04[] = {
        { 15, 0,        "AllowedBytes",      0,              },
} ;

static QBV_Field_t     P_07[] = {
        { 15, 8,        "FilteredPriorities",      0,        },
        {  7, 0,        "PolicedPriorities",       0,        },
} ;

static QBV_Field_t     P_08[] = {
        { 13,12,        "GBMode1",           0,              },
        { 11,11,        "SetSelect1",        0,              },
        { 10, 8,        "QueueSel1",         0,              },
        {  5, 4,        "GBMode0",           0,              },
        {  3, 3,        "SetSelect0",        0,              },
        {  2, 0,        "QueueSel0",         0,              },
} ;

static QBV_Register_t  P_REGS[] = {
        { 0, QBV_FIELD_SIZE(P_00), P_00, 0, 0 },
        { 1, QBV_FIELD_SIZE(P_01), P_01, 0, 0 },
        { 4, QBV_FIELD_SIZE(P_04), P_04, 0, 0 },
        { 7, QBV_FIELD_SIZE(P_07), P_07, 0, 0 },
        { 8, QBV_FIELD_SIZE(P_08), P_08, 0, 0 },
} ;

static QBV_Field_t     G_00[] = {
        {  3, 2,        "SwitchOverMode",     0,              },
        {  1, 0,        "TimeSet",            0,              },
} ;

static QBV_Field_t     G_01[] = {
        {  4, 4,        "KeepFiltering",      0,              },
        {  3, 2,        "ByteScale",          0,              },
        {  1, 0,        "TimeScale",          0,              },
} ;

static QBV_Field_t     G_02[] = {
        { 15, 0,        "TimeSetSwitchTimeLo",0,              },
} ;

static QBV_Field_t     G_03[] = {
        { 15, 0,        "TimeSetSwitchTimeHi",0,              },
} ;

static QBV_Field_t     G_07[] = {
#if 0
        { 10, 0,        "PortQbvAlarms",      0,              },
#endif
} ;

static QBV_Field_t     G_08[] = {
        { 11, 8,        "PortSel1",           0,              },
        {  3, 0,        "PortSel0",           0,              },
} ;

static QBV_Register_t  G_REGS[] = {
        { 0, QBV_FIELD_SIZE(G_00), G_00, 0, 0 },
        { 1, QBV_FIELD_SIZE(G_01), G_01, 0, 0 },
        { 2, QBV_FIELD_SIZE(G_02), G_02, 0, 0 },
        { 3, QBV_FIELD_SIZE(G_03), G_03, 0, 0 },
        { 7, QBV_FIELD_SIZE(G_07), G_07, 0, 0 },
        { 8, QBV_FIELD_SIZE(G_08), G_08, 0, 0 },
} ;

static QBV_Register_t  TE_REGS[] = {	// table update registers
        { 3, QBV_FIELD_SIZE(P_03TE), P_03TE, 0, 0 },	// table entry
} ;

static QBV_Register_t  GB_REGS[] = {	// guard band update registers
        { 3, QBV_FIELD_SIZE(P_03GB), P_03GB, 0, 0 },	// guard band
} ;

static QBV_Register_t  EXEC_REGS[] = {	// guard band update execution register
        { 2, QBV_FIELD_SIZE(P_02),   P_02,   0, 0 },
} ;

#define QBV_REGISTER_SIZE(f)	(sizeof(f)/sizeof(QBV_Register_t))

/* We can group port registers and global registers together as long as
 * they differ in their bit field textual description.
 */
static QBV_All_Registers_t	QBV_All_Registers[] = {
	/* 0 */ QBV_REGISTER_SIZE(P_REGS),   P_REGS,	// port specific registers
	/* 1 */ QBV_REGISTER_SIZE(G_REGS),   G_REGS,	// global registers
	/* 2 */ QBV_REGISTER_SIZE(TE_REGS),  TE_REGS,	// table entry registers
	/* 3 */ QBV_REGISTER_SIZE(GB_REGS),  GB_REGS,	// guard band registers
	/* 4 */ QBV_REGISTER_SIZE(EXEC_REGS),EXEC_REGS,	// table and guard band execution register
} ;

/* *********************************************************************************//**
 * @defgroup CMDT_FILE  CMDT FILE I/O processing
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_FILE
 * @{
 * *************************************************************************************/

/* *********************************************************************************//**
 * @brief       Handle PCAP file contents
 * @details     EsuQbvVal::EsuQbvVal(int argc, char* argv[])
 * @details     Class contructor derived from class Cli matches command syntax.\n
 *              On successful command match set class member 'err' to IOCTL_CMD_STATUS_OK
 *              otherwise to IOCTL_CMD_STATUS_ERROR_INVALID_COMMAND.\n
 *              Used to read and dump the contents of a file specified in the command line.
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/

EsuQbvVal::EsuQbvVal(int argc, char* argv[]) : EsuQbv(argc, argv)
{
	if_name = NULL ;
	dev_no  = 0x1F ; // Default: global address space of QBV
	ioc_cmd = OAK_IOCTL_REG_ESU_REQ ;
}

CliArgs_t * EsuQbvVal :: SetArgumentList(uint32_t *arg_list_size)
{
	// Set our own limited argument list

        *arg_list_size = sizeof(my_arg_list)/sizeof(CliArgs_t) ;
        return (my_arg_list) ;
}


int EsuQbvVal :: EvaluateArgumentList(int argc, char *argv[])
{
	uint32_t	pos, num ;

	err = EsuQbv :: EvaluateArgumentList(argc, argv) ;
	if (err == IOCTL_CMD_STATUS_OK) {
		err = ScanArgList(argc, argv, "-A", &pos, &num) ;
		if (err == IOCTL_CMD_STATUS_OK) {
			if (num > 0) {
				err = ReadActionArgumentList(&argv[pos], num,
					QBV_All_Registers, sizeof(QBV_All_Registers)/sizeof(QBV_All_Registers_t)) ;
			}
			else {
				operation = 3 ;
			}
		}
		else if (err == IOCTL_CMD_STATUS_ERROR_ARGUMENT_NOT_FOUND) {
			err = IOCTL_CMD_STATUS_OK ;
		}
	}
	return (err) ;
}

int EsuQbvVal::PrintRegisterList(bool print_data) 
{
	uint32_t	tot ;
	uint32_t	act, elem, field ;

	cout << "####  Bits Parameter"  << endl ;
	cout << "--------------------"  << endl ;

	tot = 0 ;
	for (act=0; act < sizeof(QBV_All_Registers)/sizeof(QBV_All_Registers_t); act++) {
		QBV_All_Registers_t *next = &QBV_All_Registers[act] ;

		for (elem=0; elem < next->Elements; elem++) {
			QBV_Register_t *reg = &next->Action[elem] ;

			for (field = 0; field < reg->FieldCnt; field++) {
				QBV_Field_t *bit_field = &reg->REGFlds[field] ;
				++tot ;
				cout << right << dec << setw(4) << setfill(' ') << tot << " " ;
				cout << dec << setw(2) << setfill(' ') << bit_field->b_pos_e << ":" ;
				cout << dec << setw(2) << setfill(' ') << bit_field->b_pos_s << " " ;
				cout << setw(24) << left <<  bit_field->b_descr  ;
				if (print_data == true) {
					cout << "0x" << right << hex << setw(4) << setfill('0') << bit_field->data << endl ;
				}
				else {
					cout << endl ;
				}
			}
#if 0
			if (print_data == true) {
				cout << "Page " << reg->TcamPage << " Offset " << reg->TcamOffs << " data:" <<  hex << setw(8) << setfill('0') << reg->data << endl << endl ;
			}
#endif
		}
	}
	return (0) ;
}

void EsuQbvVal::WriteRegisterList()
{
	if (dev_no == 0x1F) {	// Global register list
		if (verbose) {
			cout << "Updating global registers" << endl ;
		}
		Esu::WriteRegisterList(dev_no,&QBV_All_Registers[1]) ;	// global registers
	}
	else {	// Port specific register list for table and guard band entries 

		Esu::WriteRegisterList(dev_no,&QBV_All_Registers[0]) ;	// port registers
		if (pointer == 0x20 || pointer == 0x60) {
			if (verbose) {
				cout << "Updating guard band pointer: 0x" << hex << pointer << endl ;
			}
			Esu::WriteRegisterList(dev_no,&QBV_All_Registers[3]) ;	// guard band registers

			// use queue state of register 2 as data 
			err =  WriteQbvPortTableControl(dev_no, pointer, P_02[0].data & 0xFF) ;
		}
		else if (tab_set != 0) {
			if (verbose) {
				cout << "Updating table entry pointer: 0x" << hex << pointer << endl ;
			}
			Esu::WriteRegisterList(dev_no,&QBV_All_Registers[2]) ;	// table entry registers

			// use queue state of register 2 as data 
			err =  WriteQbvPortTableControl(dev_no, pointer, P_02[0].data & 0xFF) ;
		}
	}
}

/* *********************************************************************************//**
 * @brief       Evaluate error code from command execution
 * @details     int EsuQbvVal::postAction(void)
 * @details     Checks internal 'err' variable. If IOCTL_CMD_STATUS_OK print
 *              the number of bytes read from file.
 * @return      just returns internal member variable 'err' for evaluation.
 * *************************************************************************************/
int EsuQbvVal::postAction(void)
{ 
}

int EsuQbvVal::ModifySpecificRegisterData(void)
{
	if (G_REGS[2].update != 0 || G_REGS[3].update != 0) {
		uint32_t tlo, thi ;
		err = GetPtpGlobalTime(&tlo, &thi) ;
		if (err == 0) {
			G_REGS[2].data += tlo ;
			G_REGS[3].data += thi ;
		}
		// cout << "G_REGS[2].data=" << G_REGS[2].data << endl ;
		// cout << "G_REGS[3].data=" << G_REGS[3].data << endl ;
	}
	return (err) ;
}

int EsuQbvVal::Action(void)
{ 
	ioc_data.error = -1 ;

	if (err == 0) {

		if (operation == 3) {
			err = PrintRegisterList(false) ;
		}
		else if (SetDevice(if_name) == true) {
			SetAllRegisterData(QBV_All_Registers,sizeof(QBV_All_Registers)/sizeof(QBV_All_Registers_t)) ;
			err = ModifySpecificRegisterData() ;
			if (err == 0) {
				WriteRegisterList() ;
			}
		}
	}
	return err ;
}

/* *********************************************************************************//**
 * @brief       Command verification.
 * @details     bool EsuQbvVal::isResponsible(int argc, char* argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return      returns true on command match else false
 * *************************************************************************************/

bool EsuQbvVal::isResponsible(int argc, char* argv[])
{
	// cout << argv[0] << " " << argv[1] << endl ;
	if (	strcmp(argv[1], "qbv") == 0 ) {
		return true;
	}
	return false;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
