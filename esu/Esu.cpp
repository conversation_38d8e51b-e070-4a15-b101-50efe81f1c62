/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "../CliFactory.h"

using namespace std;


Esu::Esu(int argc, char* argv[]) : Cli::Cli(argc, argv) {
	is030Model = true;
	detectHardwareModel();
}

void Esu::detectHardwareModel() {
    FILE* fp = popen("strings /proc/device-tree/model", "r");
    if (fp) {
        char buffer[128];
        if (fgets(buffer, sizeof(buffer), fp)) {
            is030Model = strstr(buffer, "MZ03") != nullptr;
        }
        pclose(fp);
    }
}
/*
 * Port and PHY Address Mapping
 *
 * +---------------+----------------+-------------+--------------------------------+
 * | External Port | Internal Port  | PHY Address |           Description          |
 * +---------------+----------------+-------------+--------------------------------+
 * |       1       |      0x1       |      0      | User-facing physical port 1    |
 * |       2       |      0x2       |      1      | User-facing physical port 2    |
 * |       3       |      0x3       |      2      | User-facing physical port 3    |
 * |       4       |      0x4       |      3      | User-facing physical port 4    |
 * |       5       |      0x5       |      4      | User-facing physical port 5    |
 * |       6       |      0x6       |      5      | User-facing physical port 6    |
 * |       7       |      0x9       |      6      | User-facing physical port 7    |
 * |       8       |      0xa       |      7      | User-facing physical port 8    |
 * +---------------+----------------+-------------+--------------------------------+
 *
 * - External Port: Customer-visible port numbers used in CLI commands
 * - Internal Port: Hardware register addresses for switch configuration
 * - PHY Address:  Physical layer device addresses for ethernet operations
 */

using ExternalPort = uint32_t;  // 用户可见的端口号
using InternalPort = uint32_t;  // 硬件寄存器地址
using PhyAddr = uint32_t;  // 物理层设备地址


const std::map<ExternalPort, InternalPort> portMapping = {
    {1, 0x1},
    {2, 0x2}, 
    {3, 0x3},
    {4, 0x4},
    {5, 0x5},
    {6, 0x6},
    {7, 0x9},
    {8, 0xa}
};

const std::map<InternalPort, ExternalPort> reversePortMapping = {
    {0x1, 1},
    {0x2, 2},
    {0x3, 3}, 
    {0x4, 4},
    {0x5, 5},
    {0x6, 6},
    {0x9, 7},
    {0xa, 8}
};

uint32_t Esu::mapExternalToInternal(uint32_t externalPort) const {
    auto it = portMapping.find(externalPort);
    if (it != portMapping.end()) {
        return it->second;
    }
    return externalPort;
}

uint32_t Esu::mapInternalToExternal(uint32_t internalPort) const {
    auto it = reversePortMapping.find(internalPort);
    if (it != reversePortMapping.end()) {
        return it->second;
    }
    return internalPort;
}



const std::map<ExternalPort, PhyAddr> phyAddrMapping = {
    {8, 7},
    {7, 6},
    {6, 5},
    {5, 4},
    {4, 3},
    {3, 2},
    {2, 1},
    {1, 0}
};

const std::map<PhyAddr, ExternalPort> reversePhyAddrMapping = {
    {7, 8},
    {6, 7},
    {5, 6},
    {4, 5},
    {3, 4},
    {2, 3},
    {1, 2},
    {0, 1}
};

uint32_t Esu::mapExternalToPhyAddr(uint32_t externalPort) const {
    auto it = phyAddrMapping.find(externalPort);
    if (it != phyAddrMapping.end()) {
        return it->second;
    }
    return externalPort;
}

uint32_t Esu::mapPhyAddrToExternal(uint32_t phyAddr) const {
    auto it = reversePhyAddrMapping.find(phyAddr);
    if (it != reversePhyAddrMapping.end()) {
        return it->second;
    }
    return phyAddr;
}

/* *********************************************************************************//**
 * @brief       Handle ESU register IO
 * @details     Esu::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
 * @details     Perform ESU specific write register IO
 * @param [in]  dev: ESU specific device
 * @param [in]  reg: ESU register offset
 * @param [in]  data: 16 bit data
 * @return	common error code: err
 * *************************************************************************************/

int Esu::WriteIO(uint32_t dev, uint32_t reg, uint32_t data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_WR ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	ioc_data.data = data ;
	return (Ioctl(ioc_cmd, (caddr_t) &ioc_data)) ;
}

/* *********************************************************************************//**
 * @brief       Handle ESU register IO
 * @details     Esu::ReadIO(uint32_t dev, uint32_t reg, uint32_t data)
 * @details     Perform ESU specific read register IO
 * @param [in]  dev: ESU specific device
 * @param [in]  reg: ESU register offset
 * @param [in]  data: 16 bit data pointer
 * @return	common error code: err
 * *************************************************************************************/

int Esu::ReadIO(uint32_t dev, uint32_t reg, uint32_t *data)
{
	ioc_data.error = -1 ;
	ioc_data.cmd = OAK_IOCTL_REG_RD ;
	ioc_data.dev_no = dev ;
	ioc_data.offs = 0x10000 | reg ;
	err =  Ioctl(ioc_cmd, (caddr_t) &ioc_data) ;
	*data = ioc_data.data ;
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Handle ESU register IO
 * @details     Esu::CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val)
 * @details     Wait for ESU specific command completion over register IO
 * @param [in]  dev: ESU specific device
 * @param [in]  reg: ESU register offset
 * @param [in]  b:   Bit number to poll
 * @param [in]  val: Expected bit value
 * @return	0 on success or IOCTL_CMD_STATUS_ERROR_TIMEOUT on timeout
 * *************************************************************************************/

int Esu::CmdDone(uint32_t dev, uint32_t reg, uint32_t b, uint32_t val, uint32_t to)
{
	uint32_t	data ;
	
	val &= 1 ;
	err = 0 ;
	for (int i=0; i<100 && err==0 ; i++) {
		err = Esu::ReadIO(dev, reg, &data) ;
		if (err == 0 && ((data >> b) & 1) == val) {
			return(0) ;
		}
		usleep(to) ;
	}
	if (err == 0) {
		err = IOCTL_CMD_STATUS_ERROR_TIMEOUT ;
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Handle ESU register IO
 * @details     Esu::ReadRegisterList(uint32_t dev, REG_All_Registers_t *tab)
 * @details     Read a register list from register space
 * @param [in]  dev: ESU specific device
 * @param [in]  tab: Register table with tab->Elements
 * @return	common error code: err
 * *************************************************************************************/

int Esu::ReadRegisterList(uint32_t dev, REG_All_Registers_t *tab)
{
        err = 0 ;
        for (uint32_t idx=0; idx < tab->Elements && err == 0; idx++) {
                REG_Register_t *next = &tab->Action[idx] ;
		err = ReadIO(dev, next->RegOffs, &next->data) ;
        }
        return (err) ;
}

/* *********************************************************************************//**
 * @brief       Handle ESU register IO
 * @details     Esu::WriteRegisterList(uint32_t dev, REG_All_Registers_t *tab)
 * @details     Write a register list to register space
 * @param [in]  dev: ESU specific device
 * @param [in]  tab: Register table with tab->Elements
 * @return	common error code: err
 * *************************************************************************************/
int Esu::WriteRegisterList(uint32_t dev, REG_All_Registers_t *tab)
{
	for (uint32_t idx=0; idx < tab->Elements && err == 0; idx++) {
		REG_Register_t *reg = &tab->Action[idx] ;

		/* Write all registers first except table and guardband entries.
		 * These registers will be updated by table write operation below
		 */
		if (reg->update != 0) {
			err =  WriteIO(dev, reg->RegOffs, reg->data) ;
		}
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Print bit field values to console
 * @details     Esu::PrintBitField(uint32_t reg_offs, REG_Field_t *bit_field)
 * @details     Prints all bit field names and valued of bit_field table 
 * @param [in]  reg_offs : displays ESU register offset
 * @param [in]  bit_field: bit field table entry to display
 * *************************************************************************************/

void Esu::PrintBitField(uint32_t reg_offs, REG_Field_t *bit_field)
{
        uint32_t width, val ;

        width = (1 << (bit_field->b_pos_e - bit_field->b_pos_s + 1)) - 1 ;
        val = (bit_field->data & width) ; 

	cout << "Offset:0x" << setw(2) << setfill('0') << hex << reg_offs
		<< " Bits[" << setfill(' ') << dec << setw(2) << bit_field->b_pos_e
                << ":" << setfill(' ') << dec << setw(2) << bit_field->b_pos_s << "]"
                << " register data: 0x" << hex << setw(8) << setfill('0') << bit_field->data
                << " " <<  bit_field->b_descr << "(0x" << val << "," << dec << val << ")" << endl ;
}

/* *********************************************************************************//**
 * @brief       Print register table to console
 * @details     Esu::PrintRegisterList(uint32_t dev, REG_All_Registers_t *tab, uint32_t tab_size)
 * @details     Prints all bit field names and valued of bit_field table 
 * @param [in]  dev     : ESU internal device number
 * @param [in]  tab     : Register table do display
 * @param [in]  tab_size: size of register table
 * *************************************************************************************/

int Esu::PrintRegisterList(uint32_t dev, REG_All_Registers_t *tab, uint32_t tab_size)
{
	uint32_t idx ;

	for (idx=0; idx < tab_size && err == 0; idx++) {
		REG_All_Registers_t *ptab = &tab[idx] ;

		err = ReadRegisterList(dev, ptab) ;
		if (err == 0) {
        		for (uint32_t i=0; i < ptab->Elements; i++) {
                		REG_Register_t *next = &ptab->Action[i] ;
        			for (uint32_t fld=0; fld < next->FieldCnt; fld++) {
					next->REGFlds[fld].data = next->data ;
					PrintBitField(next->RegOffs, &next->REGFlds[fld]) ;
				}
        		}
        	}
	}
	return (err);
}

/* *********************************************************************************//**
 * @brief       Set bit field of register table
 * @details     Esu::SetActionParameterBits(REG_Field_t *bit_field, uint32_t val)
 * @details     Sets the given bit field's data according to the given bit offset
 * @param [in]  bit_field : Pointer to bit field table entry
 * @param [in]  val       : Given value of bit field
 * *************************************************************************************/

void Esu::SetActionParameterBits(REG_Field_t *bit_field, uint32_t val)
{
	uint32_t width ;

	/* Adapt data size according to the number of valid bits in the bit field entry
	 */
	width = (1 << (bit_field->b_pos_e - bit_field->b_pos_s + 1)) - 1 ;
	bit_field->data = (val & width) ; // << bit_field->b_pos_s ;

#if 1
	if (verbose) {
		cout << " Bits[" << setfill(' ') << dec << setw(2) << bit_field->b_pos_e
		<< ":" << setfill(' ') << dec << setw(2) << bit_field->b_pos_s << "]"
		<< " data: 0x" << hex << setw(8) << setfill('0') << bit_field->data 
		<< " " <<  bit_field->b_descr << "(0x" << val << ")" << endl ;
	}
#endif
}

/* *********************************************************************************//**
 * @brief       Set bit field of register table
 * @details     Esu::SetValidActionParameter(char *ptr, REG_All_Registers_t *tab,
 *			uint32_t tab_size)
 * @details     Scans the input string ptr for the expression <parameter>=<value>.
 *		If the parameter name matches a bit field from the register table tab
 *		the corresponding value is stored in the register table entry data field.
 * @param [in]  ptr      : Input parameter text string
 * @param [in]  tab      : register table to look up the bit field name
 * @param [in]  tab_size : register table size
 * @return	true on success or false if input string did not match
 * *************************************************************************************/

bool Esu::SetValidActionParameter(char *ptr, REG_All_Registers_t *tab, uint32_t tab_size)
{
	uint32_t act, elem, field ;

	for (act=0; act < tab_size; act++) {
		REG_All_Registers_t *next = &tab[act] ;

		for (elem=0; elem < next->Elements; elem++) {
			REG_Register_t *qbvreg = &next->Action[elem] ;

			for (field = 0; field < qbvreg->FieldCnt; field++) {
				REG_Field_t *bit_field = &qbvreg->REGFlds[field] ;

				string s = bit_field->b_descr ;
				string a = ptr ;


				std::size_t pos =  a.find(s+"=") ;


				if (pos == 0) {
					uint32_t val ;
		
					val = StringToDecHexInt(&ptr[s.size()+1]) ;
					SetActionParameterBits(bit_field, val) ;
					if (verbose) {
						cout << dec <<  "SET FIELD: " << s << " val=" << val << endl ;
					}
					qbvreg->update = 1 ;

					return (true) ;
				}
			}
		}
	}
	return (false) ;
}

/* *********************************************************************************//**
 * @brief       Scan input argument parameters
 * @details     ReadActionArgumentList(char *argv[], int pos, int num,
 *		REG_All_Registers_t *tab, uint32_t tab_size)
 * @details     Scans the input line argv for multiple expressions of <parameter>=<value>.
 *		If any parameter name matches a bit field from the register table tab
 *		the corresponding value is stored in the register table entry data field.
 * @param [in]  argv     : Input line of arguments
 * @param [in]  num      : number of entries in pointer array argv[]
 * @param [in]  tab      : register table 
 * @param [in]  tab_size : register table size
 * @return	common error code: err
 * *************************************************************************************/

int Esu::ReadActionArgumentList(char *argv[], int num, REG_All_Registers_t *tab, uint32_t tab_size) 
{
	err = 0 ;
	for (int idx = 0 ; idx < num && err == 0; idx++) {
		if (SetValidActionParameter(argv[idx], tab, tab_size) == false) {
			err = IOCTL_CMD_STATUS_ERROR_INVALID_CMDLINE_PARAMETER ;
		}
	}
	return (err) ;
}

/* *********************************************************************************//**
 * @brief       Evaluate register data 
 * @details     SetRegisterData(REG_Register_t *tab)
 * @details     Walks through the register bit fields of each register table entry and
 *		computed the resulting register value for all bit field.
 * @param [in]  tab      : register table entry
 * *************************************************************************************/

void Esu::SetRegisterData(REG_Register_t *tab)
{
	uint32_t	width ;

	if (tab->FieldCnt > 0) {
		tab->data = 0 ;
		for (int idx = 0; idx < tab->FieldCnt; idx++) {
			QBV_Field_t *bit_field = &tab->REGFlds[idx] ;
			tab->data |= bit_field->data << bit_field->b_pos_s ;
		}
#if 0
		if (verbose) {
			cout << "Offset: " << tab->RegOffs << " data: " << hex << setw(8) << setfill('0') <<  tab->data << endl ;
		}
#endif
	}
}

/* *********************************************************************************//**
 * @brief       Evaluate register data 
 * @details     SetAllRegisterData(REG_Register_t *tab)
 * @details     Walks through a register table and computes each register table entry
 *		data field according to the particular bit fields values.
 *		computed the resulting register value for all bit field.
 * @param [in]  tab      : register table 
 * @param [in]  tab_size : register table size
 * *************************************************************************************/
void Esu::SetAllRegisterData(REG_All_Registers_t *tab, uint32_t tab_size)
{
	for (int idx=0; idx < tab_size; idx++) {
		int		    elem ;
		QBV_All_Registers_t *next = &tab[idx] ;

		for (elem=0; elem < next->Elements; elem++) {
			SetRegisterData(&next->Action[elem]) ;
		}
	}
}
/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
