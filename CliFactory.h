/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#ifndef IOCTLCMDFACTORY_H
#define IOCTLCMDFACTORY_H

#include "Cli.h"

/* EXTENSION CODE:
 * For each sub command add the corresponding directory, help file and
 * command specific include files here like shown below ...
 */
#include "help/Help.h"
#include "register/RegisterIO.h"
#include "register/RegisterFileIO.h"
#include "esu/Esu.h"
#include "esu/EsuTai.h"
#include "esu/EsuPtpGlobal.h"
#include "esu/EsuPtpPort.h"
#include "esu/EsuAtu.h"
#include "esu/EsuVtu.h"
#include "esu/EsuTcam.h"
#include "esu/EsuStat.h"
#include "esu/EsuQbv.h"
#include "esu/EsuQbvVal.h"
#include "esu/EsuPhy2112.h"
#include "esu/EsuPhy1512.h"
#include "esu/EsuForward.h"
#include "misc/FileDump.h"
#include "misc/FileLoad.h"
#include "misc/FileUpLoad.h"
#include "misc/LoadGenerator.h"
#include "misc/Channel.h"
#include "misc/MsiIrq.h"
#include "misc/Set.h"
#if 1
#include "register/RxFlow.h"
#endif

/* END EXTENSION CODE 
 */

#include "Commands.h"

#define	CMD_TABLE(TYPE)	\
	if (TYPE::isResponsible(argc, argv)) return new TYPE(argc, argv)

class CliFactory
{
	public:
		Cli* CreateCli(int argc, char* argv[]);
		// static void PrintHelp(void);
};

#endif // IOCTLCMDFACTORY_H

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
