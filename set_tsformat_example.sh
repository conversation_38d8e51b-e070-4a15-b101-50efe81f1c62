#!/bin/bash

# PTP Port TS Format Configuration Example
# This script demonstrates how to set and verify the timestamp format for PTP ports

# Configuration
INTERFACE="eth0"
CLI_TOOL="./autoeth_cli"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if CLI tool exists
if [ ! -f "$CLI_TOOL" ]; then
    print_error "CLI tool '$CLI_TOOL' not found. Please compile first."
    exit 1
fi

echo "PTP Port TS Format Configuration Example"
echo "========================================"
echo "Interface: $INTERFACE"
echo ""

# Function to set TS format for a port
set_ts_format() {
    local port=$1
    local format=$2
    local format_name=$3
    
    print_info "Setting TS Format for port $port to $format ($format_name)"
    
    # Set the TS format
    $CLI_TOOL PTP_PORT -if $INTERFACE -port $port -tsformat $format -v
    
    if [ $? -eq 0 ]; then
        print_success "TS Format set successfully"
    else
        print_error "Failed to set TS Format"
        return 1
    fi
    
    # Verify the setting
    print_info "Verifying TS Format setting..."
    $CLI_TOOL PTP_PORT -if $INTERFACE -port $port -gettsformat -v
    
    echo ""
}

# Function to demonstrate TS format configuration
demonstrate_ts_format() {
    local port=$1
    
    echo "Demonstrating TS Format configuration for Port $port"
    echo "=================================================="
    
    # Show current TS format
    print_info "Current TS Format for port $port:"
    $CLI_TOOL PTP_PORT -if $INTERFACE -port $port -gettsformat -v
    echo ""
    
    # Set to global timer format (0)
    set_ts_format $port 0 "32-bit global timer"
    
    # Set to ToD format (1)
    set_ts_format $port 1 "32-bit ToD"
    
    # Show register 0x00 to see the bit change
    print_info "Reading register 0x00 to see TSFormat bit (bit 4):"
    $CLI_TOOL PTP_PORT -if $INTERFACE -port $port -rd 0x00 -v
    echo ""
}

# Main demonstration
print_info "This script demonstrates PTP Port TS Format configuration"
print_info "TS Format controls the timestamp format used by the PTP port:"
print_info "  0 = 32-bit global timer format"
print_info "  1 = 32-bit ToD (Time of Day) format"
echo ""

# Test with port 1
demonstrate_ts_format 1

# Show how to configure multiple ports
echo "Configuring Multiple Ports"
echo "=========================="

print_info "Setting all ports 1-4 to ToD format:"
for port in {1..4}; do
    print_info "Configuring port $port..."
    $CLI_TOOL PTP_PORT -if $INTERFACE -port $port -tsformat 1
    if [ $? -eq 0 ]; then
        print_success "Port $port configured"
    else
        print_error "Failed to configure port $port"
    fi
done
echo ""

print_info "Verifying configuration for all ports:"
for port in {1..4}; do
    echo -n "Port $port: "
    $CLI_TOOL PTP_PORT -if $INTERFACE -port $port -gettsformat 2>/dev/null
done
echo ""

# Show practical usage scenarios
echo "Practical Usage Scenarios"
echo "========================="

print_info "Scenario 1: Configure for IEEE 1588 PTP with ToD timestamps"
print_info "Command: $CLI_TOOL PTP_PORT -if $INTERFACE -port 1 -tsformat 1"
echo ""

print_info "Scenario 2: Configure for high-precision timing with global timer"
print_info "Command: $CLI_TOOL PTP_PORT -if $INTERFACE -port 1 -tsformat 0"
echo ""

print_info "Scenario 3: Check current configuration before making changes"
print_info "Command: $CLI_TOOL PTP_PORT -if $INTERFACE -port 1 -gettsformat"
echo ""

# Show register bit explanation
echo "Register Bit Details"
echo "==================="
print_info "TS Format is controlled by bit 4 in register 0x00:"
print_info "  Bit 4 = 0: Timestamp in 32-bit global timer"
print_info "  Bit 4 = 1: Timestamp in 32-bit 2-sec ToD or 1722"
echo ""

print_info "To manually check the bit:"
print_info "1. Read register 0x00: $CLI_TOOL PTP_PORT -if $INTERFACE -port 1 -rd 0x00"
print_info "2. Check bit 4 in the returned value"
print_info "3. If bit 4 is set (value & 0x10), TS Format is ToD"
print_info "4. If bit 4 is clear, TS Format is global timer"
echo ""

# Show related configuration
echo "Related Configuration"
echo "===================="
print_info "When using ToD format, you may also need to configure:"
print_info "1. PTP Global ToD settings (use PTP_GLOBAL command)"
print_info "2. Hardware acceleration (register 0x02, bit 6)"
print_info "3. Message type enables for timestamp capture"
echo ""

print_info "Example complete configuration for ToD timestamps:"
echo "# Set TS format to ToD"
echo "$CLI_TOOL PTP_PORT -if $INTERFACE -port 1 -tsformat 1"
echo ""
echo "# Enable hardware acceleration and set arrival mode"
echo "$CLI_TOOL PTP_PORT -if $INTERFACE -port 1 -wr 0x02 0x1FF"
echo ""
echo "# Configure transport specification for Ethernet"
echo "$CLI_TOOL PTP_PORT -if $INTERFACE -port 1 -wr 0x00 0x10"  # TSFormat=1, TransSpec=0
echo ""

print_success "TS Format configuration demonstration completed!"
print_info "Use -v flag with commands for detailed output and verification"
