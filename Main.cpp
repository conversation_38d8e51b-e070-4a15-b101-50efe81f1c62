/*******************************************************************************
 *
 *      LICENSE:
 *      (C)Copyright 2010-2011 Marvell.
 *
 *      All Rights Reserved
 *
 *      THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *      The copyright notice above does not evidence any
 *      actual or intended publication of such source code.
 *
 *      This Module contains Proprietary Information of Marvell
 *      and should be treated as Confidential.
 *
 *      The information in this file is provided for the exclusive use of
 *      the licensees of Marvell. Such users have the right to use, modify,
 *      and incorporate this code into products for purposes authorized by
 *      the license agreement provided they include this notice and the
 *      associated copyright notice with any such product.
 *
 *      The information in this file is provided "AS IS" without warranty.
 *	Author: <PERSON><PERSON> (<EMAIL>)
 *      /LICENSE
 *
 ******************************************************************************/

#include "CliFactory.h"

/* *********************************************************************************//**
 * @defgroup CMDT_MAIN	CMDT Command Tool
 * *************************************************************************************/

/* *********************************************************************************//**
 * @addtogroup CMDT_MAIN
 * @{
 * *************************************************************************************/

static CliFactory	factory ;

/* *********************************************************************************//**
 * @brief	General command parser
 * @details	void do_cmd(int argc, char *argv[])
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * *************************************************************************************/
static int do_cmd(int argc, char *argv[])
{
	int rc = -1 ;

	Cli* cmd = factory.CreateCli(argc, argv);

	if (cmd) {
		if (cmd->HandleArguments() == IOCTL_CMD_STATUS_OK) { 
			if (cmd->preAction() == IOCTL_CMD_STATUS_OK) { 
				if (cmd->Action() == IOCTL_CMD_STATUS_OK) { 
					if (cmd->postAction() != IOCTL_CMD_STATUS_OK) { 
						cerr << cmd->getErrorMsg() << endl; 
					}
					else {
						rc = 0 ;
					}
				} else {
					cerr << cmd->getErrorMsg() << endl;
				}
			} else {
				cerr << cmd->getErrorMsg() << endl;
			}
		} else {
			cerr << cmd->getErrorMsg() << endl;
		}
	}
	return (rc) ;
}

/* *********************************************************************************//**
 * @brief	Main function
 * @details	int main(int argc, char* argv[])
 * @details	check command line arguments and start command line interpreter in
 *		interactive mode or just executes a particular command line alternatively
 * @param [in]  argc command line argument count
 * @param [in]  argv command line argument vector
 * @return	0
 * *************************************************************************************/
int main(int argc, char* argv[])
{
	return (do_cmd(argc, argv)) ;
}

/* *********************************************************************************//**
 * @}
 * *************************************************************************************/

/*******************************************************************************
 *
 * End of file
 *
 ******************************************************************************/
